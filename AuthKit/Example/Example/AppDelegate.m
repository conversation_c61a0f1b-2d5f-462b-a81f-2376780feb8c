//
//  AppDelegate.m
//  Example
//
//  Created by <PERSON><PERSON><PERSON> on 2019/1/7.
//  Copyright © 2019 Wu<PERSON><PERSON>. All rights reserved.
//

#import "AppDelegate.h"
#import "Example-Swift.h"

#import <AuthKit/LZAuthKitApi.h>
#import <AuthKit/LZAuthKitViewController.h>

//#import <Logz/Logz.h>

#import <LZDeviceUtil/LZDeviceMgr.h>

@import Logz;
@import social_base;


@interface AppDelegate ()

@end

@implementation AppDelegate

- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions {
    
//    if (@available(iOS 13.0, *)) {
//    } else {
        UIWindow *window = [[UIWindow alloc] initWithFrame:[UIScreen mainScreen].bounds];
        self.window = window;
        [self initRootView:window];
        [window makeKeyAndVisible];
//    }
    
    [SwiftAppDeleagte didFinishLaunch];

    return YES;
}

- (void)setupLogOutput
{
    // 打印启动信息，强烈建议
    // error 为了尽量高层级打印信息
    // logan 启动可能会跟上一次混合出现最后一条没写完
    LogzTagE(@"Logz", @"Start Logz");
}

- (BOOL)application:(UIApplication *)app openURL:(NSURL *)url options:(NSDictionary<UIApplicationOpenURLOptionsKey,id> *)options
{
    BOOL auth = [LZAuthKit application:app openURL:url options:options];
    if (auth) {
        return YES;
    }
    
    return NO;
}

- (BOOL)application:(UIApplication *)application continueUserActivity:(NSUserActivity *)userActivity restorationHandler:(void (^)(NSArray<id<UIUserActivityRestoring>> * _Nullable))restorationHandler
{
    BOOL auth = [LZAuthKit application:application userActivity:userActivity handler:restorationHandler];
    if (auth) {
        return YES;
    }
    
    return NO;
}

- (BOOL)application:(UIApplication *)application open:(NSURL * _Nonnull)url sourceApplication:(NSString * _Nullable)sourceApplication annotation:(id _Nonnull)annotation
{
    BOOL auth = [LZAuthKit application:application openURL:url sourceApplication:sourceApplication annotation:annotation];
    if (auth) {
        return YES;
    }
    
    return NO;
}


- (void)initRootView:(UIWindow *)window {
    window.rootViewController = [[UINavigationController alloc] initWithRootViewController:[[LZAuthKitViewController alloc] init]];
    
    [self setupLogOutput];
    NSString* appID = @"10913702"; //10913702（诗朗诵） -10919088(pp约玩)
    NSString* deviceID = [LZDeviceMgr deviceId];
    LogzConfig* config = [[LogzConfig alloc] initWithAppId:appID deviceId:deviceID];
    [Logz startupWith:config];
    
    [LZAuthKit startupWithAppId:appID deviceId:deviceID];
//    [LZAuthKit setMushroomEnable:YES];
    LZAuthKit.mushroomEnable = YES;
    
}

+ (AppDelegate*)shared {
    return (AppDelegate*)UIApplication.sharedApplication.delegate;
}


@end
