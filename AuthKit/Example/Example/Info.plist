<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>1.0</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>qq</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>tencent101893536</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>wechat</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>wx076a6137de776fe9</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>sinaweibo</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>wb2609767994</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>facebook</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>fb275446340201991</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>google</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.508385281030-mvcpgqkv0so90r4qtg40tf7r2frq69jv</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>TikTokAppID</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>awvbxhvy7c6n7zve</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>authkit</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>tiyaapp</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>twitter</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>twitterkit-hLOzxjDxWOD28xQTKrgRuMxAA</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>line</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>line3rdp.com.lizhi.component.playground</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>1</string>
	<key>FacebookAppID</key>
	<string>275446340201991</string>
	<key>FacebookClientToken</key>
	<string>********************************</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>line</string>
		<string>lineauth2</string>
		<string>fbapi</string>
		<string>fb-messenger</string>
		<string>fb-messenger-api</string>
		<string>fb-messenger-share-api</string>
		<string>fbauth2</string>
		<string>fbshareextension</string>
		<string>snapchat</string>
		<string>twitter</string>
		<string>mqq</string>
		<string>weixinULAPI</string>
		<string>instagram</string>
		<string>weixin</string>
		<string>wechat</string>
		<string>wtloginmqq2</string>
		<string>mqqopensdkapiV3</string>
		<string>mqqwpa</string>
		<string>mqqopensdkapiV2</string>
		<string>mqqOpensdkSSoLogin</string>
		<string>mqqapi</string>
		<string>mqzone</string>
		<string>sinaweibo</string>
		<string>sinaweibohd</string>
		<string>sinaweibosso</string>
		<string>sinaweibohdsso</string>
		<string>weibosdk</string>
		<string>weibosdk2.5</string>
		<string>weibosdk3.3</string>
		<string>cydia</string>
		<string>SubAppsDomain</string>
		<string>echoLuckysdk</string>
		<string>phoenixfm</string>
		<string>kaolafm</string>
		<string>lazyaudio</string>
		<string>qtfmradio</string>
		<string>orpheus</string>
		<string>iting</string>
		<string>nextradio</string>
		<string>ting878445592</string>
		<string>kugouURL</string>
		<string>qqmusic</string>
		<string>tiantianptu</string>
		<string>autohome</string>
		<string>blued</string>
		<string>mogujie</string>
		<string>yykiwi</string>
		<string>douyutv</string>
		<string>faceu</string>
		<string>b612cn</string>
		<string>JuMei</string>
		<string>mtxx</string>
		<string>kuaikan</string>
		<string>myxj</string>
		<string>mlolapp</string>
		<string>thunder</string>
		<string>hanju</string>
		<string>HappyAnimal3</string>
		<string>meetyou.linggan</string>
		<string>LZWidget</string>
		<string>LizhiDevWidget</string>
		<string>LizhiHaokuWidget</string>
		<string>LizhiDevWidget2</string>
		<string>LizhiHaokuWidget2</string>
		<string>LizhiS3</string>
		<string>LizhiS7</string>
		<string>LizhiS8</string>
		<string>LizhiS9</string>
		<string>LizhiS10</string>
		<string>LizhiS11</string>
		<string>openApp.jdMobile</string>
		<string>tiktokopensdk</string>
		<string>tiktoksharesdk</string>
		<string>snssdk1180</string>
		<string>snssdk1233</string>
		<string>snapchat</string>
		<string>bitmoji-sdk</string>
		<string>itms-apps</string>
		<string>twitterauth</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>访问相机</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>访问相册</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>访问相册</string>
	<key>SCSDKClientId</key>
	<string>0e0ffea9-0744-42a2-88f5-0da1994b172f</string>
	<key>SCSDKRedirectUrl</key>
	<string>tiyaapp://snap/oauth</string>
	<key>SCSDKScopes</key>
	<array>
		<string>https://auth.snapchat.com/oauth2/api/user.display_name</string>
		<string>https://auth.snapchat.com/oauth2/api/user.external_id</string>
		<string>https://auth.snapchat.com/oauth2/api/user.bitmoji.avatar</string>
	</array>
	<key>SnapKitAutoInit</key>
	<false/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen.storyboard</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
</dict>
</plist>
