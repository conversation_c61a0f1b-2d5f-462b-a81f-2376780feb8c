// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 48;
	objects = {

/* Begin PBXBuildFile section */
		36C6E1032A0CEE2500C22A36 /* SwiftAppDeleagte.swift in Sources */ = {isa = PBXBuildFile; fileRef = 36C6E1022A0CEE2500C22A36 /* SwiftAppDeleagte.swift */; };
		56D951EA24F0F152004BFD53 /* auth.env in Resources */ = {isa = PBXBuildFile; fileRef = 56D951E924F0F152004BFD53 /* auth.env */; };
		6A6A0822FD27B04B8E99702B /* libPods-Example.a in Frameworks */ = {isa = PBXBuildFile; fileRef = D1831C9F695F87394ED9DAE5 /* libPods-Example.a */; };
		6D0B6903222E128600617148 /* LocalAuthentication.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6D0B6902222E128600617148 /* LocalAuthentication.framework */; };
		6D0B6905222E128D00617148 /* SafariServices.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6D0B6904222E128D00617148 /* SafariServices.framework */; };
		6D0B6907222E129600617148 /* SystemConfiguration.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6D0B6906222E129600617148 /* SystemConfiguration.framework */; };
		6DE89B3221E32AE7001DD75C /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 6DE89B3121E32AE7001DD75C /* AppDelegate.m */; };
		6DE89B3521E32AE7001DD75C /* ViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 6DE89B3421E32AE7001DD75C /* ViewController.m */; };
		6DE89B3821E32AE7001DD75C /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 6DE89B3621E32AE7001DD75C /* Main.storyboard */; };
		6DE89B3A21E32AEB001DD75C /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 6DE89B3921E32AEB001DD75C /* Assets.xcassets */; };
		6DE89B3D21E32AEB001DD75C /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 6DE89B3B21E32AEB001DD75C /* LaunchScreen.storyboard */; };
		6DE89B4021E32AEB001DD75C /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 6DE89B3F21E32AEB001DD75C /* main.m */; };
		8D7472BD296D623E006684EE /* SceneDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8D7472BC296D623E006684EE /* SceneDelegate.swift */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		3686F9CE2BAC593900FAD807 /* ExampleDebug.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = ExampleDebug.entitlements; sourceTree = "<group>"; };
		36C6E1022A0CEE2500C22A36 /* SwiftAppDeleagte.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SwiftAppDeleagte.swift; sourceTree = "<group>"; };
		4931EDC90762DFD1748F2F49 /* Pods-Example.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Example.release.xcconfig"; path = "Pods/Target Support Files/Pods-Example/Pods-Example.release.xcconfig"; sourceTree = "<group>"; };
		56B0EBF224DD060200FB7548 /* Example-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "Example-Bridging-Header.h"; sourceTree = "<group>"; };
		56D951E924F0F152004BFD53 /* auth.env */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = auth.env; sourceTree = "<group>"; };
		6D0B6901222D34BB00617148 /* Example.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = Example.entitlements; sourceTree = "<group>"; };
		6D0B6902222E128600617148 /* LocalAuthentication.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = LocalAuthentication.framework; path = System/Library/Frameworks/LocalAuthentication.framework; sourceTree = SDKROOT; };
		6D0B6904222E128D00617148 /* SafariServices.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SafariServices.framework; path = System/Library/Frameworks/SafariServices.framework; sourceTree = SDKROOT; };
		6D0B6906222E129600617148 /* SystemConfiguration.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SystemConfiguration.framework; path = System/Library/Frameworks/SystemConfiguration.framework; sourceTree = SDKROOT; };
		6D8C0AF3A85F3E79499ABD48 /* Pods-Example-ExampleTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Example-ExampleTests.debug.xcconfig"; path = "Pods/Target Support Files/Pods-Example-ExampleTests/Pods-Example-ExampleTests.debug.xcconfig"; sourceTree = "<group>"; };
		6DE89B2D21E32AE7001DD75C /* Example.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Example.app; sourceTree = BUILT_PRODUCTS_DIR; };
		6DE89B3021E32AE7001DD75C /* AppDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		6DE89B3121E32AE7001DD75C /* AppDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AppDelegate.m; sourceTree = "<group>"; };
		6DE89B3321E32AE7001DD75C /* ViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ViewController.h; sourceTree = "<group>"; };
		6DE89B3421E32AE7001DD75C /* ViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ViewController.m; sourceTree = "<group>"; };
		6DE89B3721E32AE7001DD75C /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		6DE89B3921E32AEB001DD75C /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		6DE89B3C21E32AEB001DD75C /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		6DE89B3E21E32AEB001DD75C /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		6DE89B3F21E32AEB001DD75C /* main.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		8D7472BC296D623E006684EE /* SceneDelegate.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SceneDelegate.swift; sourceTree = "<group>"; };
		D1831C9F695F87394ED9DAE5 /* libPods-Example.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-Example.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		D60688BEF2A6922E77CC3BAB /* Pods-Example.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Example.debug.xcconfig"; path = "Pods/Target Support Files/Pods-Example/Pods-Example.debug.xcconfig"; sourceTree = "<group>"; };
		DFF55C4C8EE8F66A2A2770B7 /* Pods-Example-ExampleTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Example-ExampleTests.release.xcconfig"; path = "Pods/Target Support Files/Pods-Example-ExampleTests/Pods-Example-ExampleTests.release.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		6DE89B2A21E32AE7001DD75C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6D0B6907222E129600617148 /* SystemConfiguration.framework in Frameworks */,
				6D0B6905222E128D00617148 /* SafariServices.framework in Frameworks */,
				6D0B6903222E128600617148 /* LocalAuthentication.framework in Frameworks */,
				6A6A0822FD27B04B8E99702B /* libPods-Example.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		6DE89B2421E32AE7001DD75C = {
			isa = PBXGroup;
			children = (
				6DE89B2F21E32AE7001DD75C /* Example */,
				6DE89B2E21E32AE7001DD75C /* Products */,
				89F78C0C1D135151EFEA74ED /* Pods */,
				C1EDB1D7E9CBF1443C27B74F /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		6DE89B2E21E32AE7001DD75C /* Products */ = {
			isa = PBXGroup;
			children = (
				6DE89B2D21E32AE7001DD75C /* Example.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		6DE89B2F21E32AE7001DD75C /* Example */ = {
			isa = PBXGroup;
			children = (
				3686F9CE2BAC593900FAD807 /* ExampleDebug.entitlements */,
				8D7472BC296D623E006684EE /* SceneDelegate.swift */,
				36C6E1022A0CEE2500C22A36 /* SwiftAppDeleagte.swift */,
				56D951E924F0F152004BFD53 /* auth.env */,
				6D0B6901222D34BB00617148 /* Example.entitlements */,
				6DE89B3021E32AE7001DD75C /* AppDelegate.h */,
				6DE89B3121E32AE7001DD75C /* AppDelegate.m */,
				6DE89B3321E32AE7001DD75C /* ViewController.h */,
				6DE89B3421E32AE7001DD75C /* ViewController.m */,
				6DE89B3621E32AE7001DD75C /* Main.storyboard */,
				6DE89B3921E32AEB001DD75C /* Assets.xcassets */,
				6DE89B3B21E32AEB001DD75C /* LaunchScreen.storyboard */,
				6DE89B3E21E32AEB001DD75C /* Info.plist */,
				6DE89B3F21E32AEB001DD75C /* main.m */,
				56B0EBF224DD060200FB7548 /* Example-Bridging-Header.h */,
			);
			path = Example;
			sourceTree = "<group>";
		};
		89F78C0C1D135151EFEA74ED /* Pods */ = {
			isa = PBXGroup;
			children = (
				D60688BEF2A6922E77CC3BAB /* Pods-Example.debug.xcconfig */,
				4931EDC90762DFD1748F2F49 /* Pods-Example.release.xcconfig */,
				6D8C0AF3A85F3E79499ABD48 /* Pods-Example-ExampleTests.debug.xcconfig */,
				DFF55C4C8EE8F66A2A2770B7 /* Pods-Example-ExampleTests.release.xcconfig */,
			);
			name = Pods;
			sourceTree = "<group>";
		};
		C1EDB1D7E9CBF1443C27B74F /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				6D0B6906222E129600617148 /* SystemConfiguration.framework */,
				6D0B6904222E128D00617148 /* SafariServices.framework */,
				6D0B6902222E128600617148 /* LocalAuthentication.framework */,
				D1831C9F695F87394ED9DAE5 /* libPods-Example.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		6DE89B2C21E32AE7001DD75C /* Example */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 6DE89B4321E32AEB001DD75C /* Build configuration list for PBXNativeTarget "Example" */;
			buildPhases = (
				329EBCBBEB10F1694B539F1B /* [CP] Check Pods Manifest.lock */,
				36C6E1012A0CEB1000C22A36 /* ShellScript */,
				6DE89B2921E32AE7001DD75C /* Sources */,
				6DE89B2A21E32AE7001DD75C /* Frameworks */,
				6DE89B2B21E32AE7001DD75C /* Resources */,
				E17C202EB703A91B941E8413 /* [CP] Copy Pods Resources */,
				6D31B4B74755A2126CEBF448 /* [CP] Embed Pods Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Example;
			productName = Example;
			productReference = 6DE89B2D21E32AE7001DD75C /* Example.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		6DE89B2521E32AE7001DD75C /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1140;
				LastUpgradeCheck = 1200;
				ORGANIZATIONNAME = WuJie;
				TargetAttributes = {
					6DE89B2C21E32AE7001DD75C = {
						CreatedOnToolsVersion = 10.1;
						LastSwiftMigration = 1160;
						ProvisioningStyle = Manual;
						SystemCapabilities = {
							com.apple.Keychain = {
								enabled = 0;
							};
						};
					};
				};
			};
			buildConfigurationList = 6DE89B2821E32AE7001DD75C /* Build configuration list for PBXProject "Example" */;
			compatibilityVersion = "Xcode 8.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 6DE89B2421E32AE7001DD75C;
			productRefGroup = 6DE89B2E21E32AE7001DD75C /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				6DE89B2C21E32AE7001DD75C /* Example */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		6DE89B2B21E32AE7001DD75C /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6DE89B3D21E32AEB001DD75C /* LaunchScreen.storyboard in Resources */,
				56D951EA24F0F152004BFD53 /* auth.env in Resources */,
				6DE89B3A21E32AEB001DD75C /* Assets.xcassets in Resources */,
				6DE89B3821E32AE7001DD75C /* Main.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		329EBCBBEB10F1694B539F1B /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Example-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		36C6E1012A0CEB1000C22A36 /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "# Type a script or drag a script file from your workspace to insert its path.\nosascript -e 'tell application \"Simulator\" to if it is running then quit'\n";
		};
		6D31B4B74755A2126CEBF448 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Example/Pods-Example-frameworks.sh",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/SnapChatSDK/Core/SCSDKCoreKit.framework/SCSDKCoreKit",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/SnapChatSDK/Login/SCSDKLoginKit.framework/SCSDKLoginKit",
			);
			name = "[CP] Embed Pods Frameworks";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/SCSDKCoreKit.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/SCSDKLoginKit.framework",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Example/Pods-Example-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		E17C202EB703A91B941E8413 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Example/Pods-Example-resources.sh",
				"${PODS_ROOT}/GTOneLoginSDK/Frameworks/OneLoginResource.bundle",
				"${PODS_ROOT}/GeeTestAuth/Frameworks/GTCaptcha4.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/GoogleSignIn/GoogleSignIn.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/LineSDKSwift/LineSDK.bundle",
				"${PODS_ROOT}/QQConnectSDK/TencentOpenApi_IOS_Bundle.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/Teki/Teki.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/TikTokOpenAuthSDK/TikTokOpenAuthSDKPrivacyInfo.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/TikTokOpenSDKCore/TikTokOpenSDKCorePrivacyInfo.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/TikTokOpenShareSDK/TikTokOpenShareSDKPrivacyInfo.bundle",
				"${PODS_ROOT}/TwitterKit/Frameworks/TwitterKit.xcframework/ios-arm64/TwitterKit.framework/TwitterKitResources.bundle",
				"${PODS_ROOT}/WeiboSDK/Resources/WeiboSDK.bundle",
			);
			name = "[CP] Copy Pods Resources";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/OneLoginResource.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/GTCaptcha4.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/GoogleSignIn.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/LineSDK.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/TencentOpenApi_IOS_Bundle.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/Teki.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/TikTokOpenAuthSDKPrivacyInfo.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/TikTokOpenSDKCorePrivacyInfo.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/TikTokOpenShareSDKPrivacyInfo.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/TwitterKitResources.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/WeiboSDK.bundle",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Example/Pods-Example-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		6DE89B2921E32AE7001DD75C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6DE89B3521E32AE7001DD75C /* ViewController.m in Sources */,
				36C6E1032A0CEE2500C22A36 /* SwiftAppDeleagte.swift in Sources */,
				6DE89B4021E32AEB001DD75C /* main.m in Sources */,
				6DE89B3221E32AE7001DD75C /* AppDelegate.m in Sources */,
				8D7472BD296D623E006684EE /* SceneDelegate.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		6DE89B3621E32AE7001DD75C /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				6DE89B3721E32AE7001DD75C /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		6DE89B3B21E32AEB001DD75C /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				6DE89B3C21E32AEB001DD75C /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		6DE89B4121E32AEB001DD75C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		6DE89B4221E32AEB001DD75C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		6DE89B4421E32AEB001DD75C /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = D60688BEF2A6922E77CC3BAB /* Pods-Example.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Example/ExampleDebug.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 7S6A96JNUX;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = Example/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = com.lizhi.component.playground;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "Infra Client Playground Dev 20241016_2";
				SWIFT_OBJC_BRIDGING_HEADER = "Example/Example-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		6DE89B4521E32AEB001DD75C /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4931EDC90762DFD1748F2F49 /* Pods-Example.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Example/Example.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				DEVELOPMENT_TEAM = GUT2NF3JML;
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = GUT2NF3JML;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = Example/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = fm.lizhi.authkit.example;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = fm.lizhi.authkit.example;
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = fm.lizhi.authkit.example;
				SWIFT_OBJC_BRIDGING_HEADER = "Example/Example-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		6DE89B2821E32AE7001DD75C /* Build configuration list for PBXProject "Example" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				6DE89B4121E32AEB001DD75C /* Debug */,
				6DE89B4221E32AEB001DD75C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		6DE89B4321E32AEB001DD75C /* Build configuration list for PBXNativeTarget "Example" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				6DE89B4421E32AEB001DD75C /* Debug */,
				6DE89B4521E32AEB001DD75C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 6DE89B2521E32AE7001DD75C /* Project object */;
}
