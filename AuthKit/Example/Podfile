# Uncomment the next line to define a global platform for your project
source '*******************:iOSPods/LizhiSpecs.git'
#source 'https://cdn.cocoapods.org'

platform :ios, '13.0'
def development_pods
  
  pod 'BaseTool/Environment'
  pod 'LZUniqueIdentifierManager', modular_headers: true
  pod 'LZDeviceUtil'
  pod 'Teki/Environment'
  
end

target 'Example' do
  
  development_pods()
  
  #  pod 'MLeakFinder', '0.2.5', :configurations => ['Debug']
#  pod 'AuthKit', :path => '../', :subspecs => ['Authentication','ExampleExtension','Facebook','QQ','WeChat', 'Weibo',  'Apple', 'Google','TikTok','Twitter','GTOneLogin'] #

  pod 'AuthKit', :path => '../', :subspecs => ['Authentication', 'ExampleExtension', 'Twitter', 'TwitterOAuth2', 'Google', 'SnapChat', 'TikTok', 'GTOneLogin', 'Facebook', 'Weibo', 'WeChat', 'QQ', 'Apple', 'Line'] #

  pod 'social_base', :git => '*******************:component_ios/social_base', :branch => 'feature/1.9.0_line'
#  pod 'social_base', :path => '../../social_base'
  
  pod 'TwitterKit', :git => '*******************:iOSPods/TwitterKit.git', :branch => '3.4.3_for_fishing'
  pod 'Logz', '0.1.12'

end


def fix_pod_deployment_target(installer)
  installer.pods_project.targets.each do |target|
    target.build_configurations.each do |config|
      config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '13.0'
    end
  end
end

post_install do |installer|
  installer.pods_project.build_configurations.each do |config|
    config.build_settings["EXCLUDED_ARCHS[sdk=iphonesimulator*]"] = "arm64"
  end
  fix_pod_deployment_target(installer)
end

