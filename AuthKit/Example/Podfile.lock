PODS:
  - AppAuth (1.5.0):
    - AppAuth/Core (= 1.5.0)
    - AppAuth/ExternalUserAgent (= 1.5.0)
  - AppAuth/Core (1.5.0)
  - AppAuth/ExternalUserAgent (1.5.0):
    - AppAuth/Core
  - AuthKit/Apple (2.8.2):
    - AuthKit/Core
  - AuthKit/Authentication (2.8.2):
    - AuthKit/Core
  - AuthKit/Core (2.8.2):
    - BaseTool/Environment (>= 1.3.7)
    - BaseTool/Log (>= 1.3.7)
    - BaseTool/Tracker (>= 1.3.7)
    - LZUniqueIdentifierManager
    - Mushroom (>= 1.1.0)
    - social_base/Core
  - AuthKit/ExampleExtension (2.8.2):
    - AuthKit/Authentication
    - AuthKit/Core
    - GeeTestAuth
  - AuthKit/Facebook (2.8.2):
    - AuthKit/Core
    - social_base/Facebook
  - AuthKit/Google (2.8.2):
    - AuthKit/Core
    - social_base/Google
  - AuthKit/GTOneLogin (2.8.2):
    - AuthKit/Core
    - social_base/GTOneLogin
  - AuthKit/Line (2.8.2):
    - AuthKit/Core
    - social_base/Line
  - AuthKit/QQ (2.8.2):
    - AuthKit/Core
    - social_base/QQ
  - AuthKit/SnapChat (2.8.2):
    - AuthKit/Core
    - SnapChatSDK/Login
    - social_base/SnapChat
  - AuthKit/TikTok (2.8.2):
    - AuthKit/Core
    - social_base/TikTok
    - TikTokOpenAuthSDK (>= 2.0.0)
  - AuthKit/Twitter (2.8.2):
    - AuthKit/Core
    - social_base/Twitter
  - AuthKit/TwitterOAuth2 (2.8.2):
    - AuthKit/Core
    - social_base/TwitterOAuth2 (>= 1.6.0)
  - AuthKit/WeChat (2.8.2):
    - AuthKit/Core
    - social_base/WeChat
  - AuthKit/Weibo (2.8.2):
    - AuthKit/Core
    - social_base/Weibo
  - BaseTool (1.4.10):
    - BaseTool/Common (= 1.4.10)
    - BaseTool/Environment (= 1.4.10)
    - BaseTool/Log (= 1.4.10)
    - BaseTool/native (= 1.4.10)
    - BaseTool/NTP (= 1.4.10)
    - BaseTool/Tracker (= 1.4.10)
  - BaseTool/Common (1.4.10):
    - BaseTool/Log
    - BaseTool/NTP
    - BaseTool/Tracker
  - BaseTool/Environment (1.4.10):
    - BaseTool/Common
    - BaseTool/Log
    - BaseTool/native
  - BaseTool/Log (1.4.10)
  - BaseTool/native (1.4.10)
  - BaseTool/NTP (1.4.10):
    - BaseTool/Log
    - BaseTool/Tracker
  - BaseTool/Tracker (1.4.10):
    - BaseTool/Log
    - BaseTool/native
  - cjson (1.7.15)
  - CloudConfig (2.0.1):
    - CloudConfig/cjson (= 2.0.1)
    - CloudConfig/core (= 2.0.1)
    - CloudConfig/ios (= 2.0.1)
  - CloudConfig/cjson (2.0.1)
  - CloudConfig/core (2.0.1):
    - BaseTool/native (>= 1.4)
    - CloudConfig/cjson
  - CloudConfig/ios (2.0.1):
    - BaseTool/Environment
    - BaseTool/Log
    - BaseTool/Tracker
    - CloudConfig/core
  - FacebookSDK (15.0.0)
  - GeeTestAuth (1.0.1):
    - BaseTool (>= 1.1.6)
  - GoogleSignIn (7.0.0):
    - AppAuth (~> 1.5)
    - GTMAppAuth (< 3.0, ~> 1.3)
    - GTMSessionFetcher/Core (< 4.0, ~> 1.1)
  - GTMAppAuth (1.3.0):
    - AppAuth/Core (~> 1.4)
    - GTMSessionFetcher/Core (~> 1.5)
  - GTMSessionFetcher/Core (1.7.2)
  - GTOneLoginSDK (2.8.6.1)
  - LineSDKSwift/Core (5.11.0)
  - LineSDKSwift/ObjC (5.11.0):
    - LineSDKSwift/Core
  - LMmap (0.1.3):
    - LMmap/ljson (= 0.1.3)
    - LMmap/mbedtls (= 0.1.3)
  - LMmap/ljson (0.1.3):
    - cjson
    - LMmap/mbedtls
  - LMmap/mbedtls (0.1.3)
  - Logz (0.1.12):
    - Logz/Logger (= 0.1.12)
    - Logz/Public (= 0.1.12)
    - Logz/Upload (= 0.1.12)
  - Logz/Logger (0.1.12):
    - BaseTool
    - LMmap
  - Logz/Public (0.1.12):
    - CloudConfig
    - Logz/Logger
    - Logz/Upload
  - Logz/Upload (0.1.12):
    - Logz/Logger
    - SSZip
  - LZDeviceid (0.0.1)
  - LZDeviceUtil (0.4.0):
    - LZDeviceid
    - LZNetworkUtil
  - LZNetworkUtil (0.2.3):
    - LZNetworkUtil/LizhiReachability (= 0.2.3)
    - LZNetworkUtil/LZReachability (= 0.2.3)
    - LZNetworkUtil/LZTraceRoute (= 0.2.3)
  - LZNetworkUtil/LizhiReachability (0.2.3)
  - LZNetworkUtil/LZReachability (0.2.3)
  - LZNetworkUtil/LZTraceRoute (0.2.3)
  - LZUniqueIdentifierManager (1.2.1)
  - Mushroom (1.1.6):
    - BaseTool (~> 1.1)
    - MushRoomSO (~> 1.1)
  - MushRoomSO (1.1.1):
    - OpenSSL (= 0.0.1)
  - OpenSSL (0.0.1)
  - QQConnectSDK (3.5.14)
  - SnapChatSDK/Core (2.4.0)
  - SnapChatSDK/Login (2.4.0):
    - SnapChatSDK/Core
  - social_base (1.8.0):
    - social_base/Core (= 1.8.0)
  - social_base/Core (1.8.0)
  - social_base/Facebook (1.8.0):
    - FacebookSDK (= 15.0.0)
    - social_base/Core
  - social_base/Google (1.8.0):
    - GoogleSignIn (~> 7.0.0)
    - social_base/Core
  - social_base/GTOneLogin (1.8.0):
    - GTOneLoginSDK (~> 2.8.3)
    - social_base/Core
  - social_base/Line (1.8.0):
    - LineSDKSwift/ObjC
    - social_base/Core
  - social_base/QQ (1.8.0):
    - QQConnectSDK (~> 3.5.14)
    - social_base/Core
  - social_base/SnapChat (1.8.0):
    - SnapChatSDK/Core (~> 2.4.0)
    - social_base/Core
  - social_base/TikTok (1.8.0):
    - social_base/Core
    - TikTokOpenSDKCore (~> 2.0)
    - TikTokOpenShareSDK (~> 2.0)
  - social_base/Twitter (1.8.0):
    - social_base/Core
    - TwitterKit (~> 3.4.2)
  - social_base/TwitterOAuth2 (1.8.0):
    - social_base/Core
  - social_base/WeChat (1.8.0):
    - social_base/Core
    - WeChatSDK (~> 2.0.2)
  - social_base/Weibo (1.8.0):
    - social_base/Core
    - WeiboSDK (~> 3.3.5)
  - SSZip (1.0.3)
  - Teki/Core (0.1.8):
    - BaseTool/Common
  - Teki/Environment (0.1.8):
    - BaseTool/Environment
    - Teki/Core
  - TikTokOpenAuthSDK (2.5.0):
    - TikTokOpenAuthSDK/Auth (= 2.5.0)
    - TikTokOpenSDKCore (= 2.5.0)
  - TikTokOpenAuthSDK/Auth (2.5.0):
    - TikTokOpenSDKCore (= 2.5.0)
  - TikTokOpenSDKCore (2.5.0):
    - TikTokOpenSDKCore/Core (= 2.5.0)
  - TikTokOpenSDKCore/Core (2.5.0)
  - TikTokOpenShareSDK (2.5.0):
    - TikTokOpenSDKCore (= 2.5.0)
    - TikTokOpenShareSDK/Share (= 2.5.0)
  - TikTokOpenShareSDK/Share (2.5.0):
    - TikTokOpenSDKCore (= 2.5.0)
  - TwitterKit (3.4.3-fishing)
  - WeChatSDK (2.0.4)
  - WeiboSDK (3.3.5)

DEPENDENCIES:
  - AuthKit/Apple (from `../`)
  - AuthKit/Authentication (from `../`)
  - AuthKit/ExampleExtension (from `../`)
  - AuthKit/Facebook (from `../`)
  - AuthKit/Google (from `../`)
  - AuthKit/GTOneLogin (from `../`)
  - AuthKit/Line (from `../`)
  - AuthKit/QQ (from `../`)
  - AuthKit/SnapChat (from `../`)
  - AuthKit/TikTok (from `../`)
  - AuthKit/Twitter (from `../`)
  - AuthKit/TwitterOAuth2 (from `../`)
  - AuthKit/WeChat (from `../`)
  - AuthKit/Weibo (from `../`)
  - BaseTool/Environment
  - Logz (= 0.1.12)
  - LZDeviceUtil
  - LZUniqueIdentifierManager
  - "social_base (from `*******************:component_ios/social_base`, branch `feature/1.9.0_line`)"
  - Teki/Environment
  - "TwitterKit (from `*******************:iOSPods/TwitterKit.git`, branch `3.4.3_for_fishing`)"

SPEC REPOS:
  "*******************:iOSPods/LizhiSpecs.git":
    - AppAuth
    - BaseTool
    - cjson
    - CloudConfig
    - FacebookSDK
    - GeeTestAuth
    - GoogleSignIn
    - GTMAppAuth
    - GTMSessionFetcher
    - GTOneLoginSDK
    - LineSDKSwift
    - LMmap
    - Logz
    - LZDeviceid
    - LZDeviceUtil
    - LZNetworkUtil
    - LZUniqueIdentifierManager
    - Mushroom
    - MushRoomSO
    - OpenSSL
    - QQConnectSDK
    - SnapChatSDK
    - SSZip
    - Teki
    - TikTokOpenAuthSDK
    - TikTokOpenSDKCore
    - TikTokOpenShareSDK
    - WeChatSDK
    - WeiboSDK

EXTERNAL SOURCES:
  AuthKit:
    :path: "../"
  social_base:
    :branch: feature/1.9.0_line
    :git: "*******************:component_ios/social_base"
  TwitterKit:
    :branch: 3.4.3_for_fishing
    :git: "*******************:iOSPods/TwitterKit.git"

CHECKOUT OPTIONS:
  social_base:
    :commit: e37bfe85e7b0198602b952c15c87eb1517f33c8e
    :git: "*******************:component_ios/social_base"
  TwitterKit:
    :commit: 1755e60624ff0353cf6f65ce8079a771bf54ca07
    :git: "*******************:iOSPods/TwitterKit.git"

SPEC CHECKSUMS:
  AppAuth: e81b5894ab4c4948d3f81476844c1039e18205f6
  AuthKit: 05f8e2d275bc259eef4d68e01ffa044ffd74fe0e
  BaseTool: 43500d1d0cdcc5e18185600f1064fce40ff2a95a
  cjson: 342d362076215ee5aeab5b3777bcd4c6380ad9a8
  CloudConfig: 0003277de4c5c1005182dbe59b5af6b3f043e7da
  FacebookSDK: 35fff2cc28113e8ae95fac418a3791c095815b22
  GeeTestAuth: da90837823492b17d414cb8c5640959fee326a6e
  GoogleSignIn: 615ee8786d127790da8c5aa21d6f6e8a01d003b1
  GTMAppAuth: a22018e958d4fb63c21885ea1cc5c12980f23f2c
  GTMSessionFetcher: f847c93cbd083d4e12893e20e42643868eb2c1e9
  GTOneLoginSDK: 5801a2bd7831335c2a04dc59b46cf0335684bc90
  LineSDKSwift: 5e1779cc6e33cfdb01b7a505e7d4c6a9ad72a656
  LMmap: 35bf4f313fa2f213b6e00ba71a385620e4d8417f
  Logz: fe5e93553de7d6e8e291482810ca3b1287b629a8
  LZDeviceid: 7361ea59ddf3d3bdc2374c00b8491abc9b2c8d6d
  LZDeviceUtil: 36140035f850fc3dcb867e67725bf3dd107d7d8e
  LZNetworkUtil: f9e07102bc5c9c7802ef38cdd7b44075c47212d9
  LZUniqueIdentifierManager: a9715bf4a775255281439af300a7a75bc33d4c91
  Mushroom: e10d48d5318be593c65d81465a2211c051d480d2
  MushRoomSO: b3208bb7dfcb1dd57814016825098ae6eaa1bb0e
  OpenSSL: 3fb9dbddd1b6bf04eaa21bf353e680baa87664c7
  QQConnectSDK: 968ac609072d3f1700d160ed0be1d45a8ad7f59c
  SnapChatSDK: f28deb08586047aa2974220e07f30f9ff8552372
  social_base: d049d90ba022ae383f0178cf45e1d3d541652e05
  SSZip: d833347ae20f180d0868f36306bdd99d653bb4cd
  Teki: 418b377cc050fc448bd0502e6556109bc8edce23
  TikTokOpenAuthSDK: f199fc7dd9004cbff16b9157087588b860aa9eab
  TikTokOpenSDKCore: 975ca6b64d4ffd6b9439fe7190a9bbc831f67289
  TikTokOpenShareSDK: 9325fad71586566802903582f272aaaa0a515dd1
  TwitterKit: df897d4b6d435d9455f83163f4071c063f90d025
  WeChatSDK: 48abc9d85a5640e17ff7fef8f8f63190c982ade5
  WeiboSDK: 9444137b7a9ac2b115d4dd4bfb75f3e2a50d6c1d

PODFILE CHECKSUM: 16c826f2fc26510af7253e9df30cdef2c2a91180

COCOAPODS: 1.16.2
