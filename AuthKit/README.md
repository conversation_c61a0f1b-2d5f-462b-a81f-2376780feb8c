# LZAuthKit

[![CI Status](https://img.shields.io/travis/dingyutao/LZAuthKit.svg?style=flat)](https://travis-ci.org/dingyutao/LZAuthKit)
[![Version](https://img.shields.io/cocoapods/v/LZAuthKit.svg?style=flat)](https://cocoapods.org/pods/LZAuthKit)
[![License](https://img.shields.io/cocoapods/l/LZAuthKit.svg?style=flat)](https://cocoapods.org/pods/LZAuthKit)
[![Platform](https://img.shields.io/cocoapods/p/LZAuthKit.svg?style=flat)](https://cocoapods.org/pods/LZAuthKit)

## Example

To run the example project, clone the repo, and run `pod install` from the Example directory first.

## Requirements

## Installation

LZAuthKit is available through [CocoaPods](https://cocoapods.org). To install
it, simply add the following line to your Podfile:

```ruby
pod 'LZAuthKit'
```

## Author

dingyutao, <EMAIL>

## License

LZAuthKit is available under the MIT license. See the LICENSE file for more info.
