#
# Be sure to run `pod lib lint LZAuthKit.podspec' to ensure this is a
# valid spec before submitting.
#
# Any lines starting with a # are optional, but their use is encouraged
# To learn more about a Podspec see https://guides.cocoapods.org/syntax/podspec.html
#

Pod::Spec.new do |s|
  s.name             = 'AuthKit'
  s.version          = '2.8.2'
  s.summary          = 'A short description of AuthKit.'
  
  s.description      = <<-DESC
TODO: Add long description of the pod here.
                       DESC
                       
  s.homepage         = 'https://gitlab.lizhi.fm/component_ios/AuthKit'
  s.license          = { :type => 'MIT', :file => 'LICENSE' }
  s.author           = { 'dingyutao' => '<EMAIL>' }
  s.source           = { :git => '*******************:component_ios/AuthKit.git', :tag => s.version.to_s }

  s.ios.deployment_target = '10.0'
#  s.pod_target_xcconfig = { 'EXCLUDED_ARCHS[sdk=iphonesimulator*]' => 'arm64'}
#  s.user_target_xcconfig = { 'EXCLUDED_ARCHS[sdk=iphonesimulator*]' => 'arm64' }
  s.swift_version = '5.0'

# temp for lint usage
  s.pod_target_xcconfig = {
    'DEFINES_MODULE' => 'YES',
    'OTHER_CFLAGS' => '-fcxx-modules',
    'EXCLUDED_ARCHS[sdk=iphonesimulator*]' => "arm64"
  }
  
  s.subspec 'Core' do |sp|
    sp.source_files = ['Sources/Classes/Core/*.{h,m,swift}',
                       'Sources/Classes/Core/Auth/*.{h,m,swift}']
    sp.dependency 'social_base/Core'
    sp.dependency 'LZUniqueIdentifierManager'
    sp.dependency 'BaseTool/Environment', '>= 1.3.7'
    sp.dependency 'BaseTool/Log', '>= 1.3.7'
    sp.dependency 'BaseTool/Tracker', '>= 1.3.7'
    sp.dependency 'Mushroom', '>= 1.1.0'
  end
  
  s.subspec 'QQ' do |sp|
    sp.source_files = 'Sources/Classes/QQ/*{h,m}'
    sp.dependency 'AuthKit/Core'
    sp.dependency 'social_base/QQ'
  end
  
  s.subspec 'WeChat' do |sp|
    sp.source_files = 'Sources/Classes/WeChat/*.{h,m}'
    sp.dependency 'AuthKit/Core'
    sp.dependency 'social_base/WeChat'
  end
  
  s.subspec 'Weibo' do |sp|
    sp.source_files = 'Sources/Classes/Weibo/*.{h,m}'
    sp.dependency 'AuthKit/Core'
    sp.dependency 'social_base/Weibo'
  end
  
  s.subspec 'Facebook' do |sp|
    sp.source_files = 'Sources/Classes/Facebook/*.{h,m}'
    sp.dependency 'AuthKit/Core'
    sp.dependency 'social_base/Facebook'
  end
  
  s.subspec 'Google' do |sp|
    sp.source_files = 'Sources/Classes/Google/*.{h,m}'
    sp.dependency 'AuthKit/Core'
    sp.dependency 'social_base/Google'
  end
  
  s.subspec 'Apple' do |sp|
    sp.source_files = 'Sources/Classes/Apple/*.{h,m}'
    sp.dependency 'AuthKit/Core'
  end
  
  s.subspec 'GTOneLogin' do |sp|
    sp.source_files = 'Sources/Classes/GTOneLogin/*.{h,m}'
    sp.dependency 'AuthKit/Core'
    sp.dependency 'social_base/GTOneLogin'
  end
    
  s.subspec 'Authentication' do |sp|
    sp.source_files = 'Sources/Classes/Authentication/*.{h,m,swift}'
    sp.dependency 'AuthKit/Core'
  end

  s.subspec 'TikTok' do |sp|
    sp.ios.deployment_target = '11.0'
    sp.source_files = 'Sources/Classes/TikTok/*.{h,m,swift}'
    sp.dependency 'AuthKit/Core'
    sp.dependency 'social_base/TikTok'
    sp.dependency 'TikTokOpenAuthSDK', '>= 2.0.0'
  end

  s.subspec 'SnapChat' do |sp|
    sp.ios.deployment_target = '11.0'
    sp.source_files = 'Sources/Classes/SnapChat/*.{h,m}'
    sp.dependency 'AuthKit/Core'
    sp.dependency 'social_base/SnapChat'
    sp.dependency 'SnapChatSDK/Login'
  end

  s.subspec 'ExampleExtension' do |sp|
    sp.source_files = 'Sources/ExampleExtension/*.{h,m,swift}'
    sp.dependency 'AuthKit/Core'
    sp.dependency 'AuthKit/Authentication'
    sp.dependency 'GeeTestAuth'
  end
  
  s.subspec 'Twitter' do |sp|
    sp.source_files = 'Sources/Classes/Twitter/*{h,m}'
    sp.dependency 'AuthKit/Core'
    sp.dependency 'social_base/Twitter'
  end
  
  s.subspec 'TwitterOAuth2' do |sp|
    sp.source_files = 'Sources/Classes/TwitterOAuth2/*{h,m}'
    sp.dependency 'AuthKit/Core'
    sp.dependency 'social_base/TwitterOAuth2', '>= 1.6.0'
  end
  
  s.subspec 'Line' do |sp|
    s.ios.deployment_target = '13.0'
    sp.source_files = 'Sources/Classes/Line/*{h,m}'
    sp.dependency 'AuthKit/Core'
    sp.dependency 'social_base/Line'
  end
  
  s.default_subspec = 'Core'
  
end
