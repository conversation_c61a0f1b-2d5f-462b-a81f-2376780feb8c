//
//  LZAuthAucViewController.m
//  LZAccountKit
//
//  Created by dingyutao on 03/13/2020.
//  Copyright (c) 2020 dingyutao. All rights reserved.
//

#import "LZAuthAucViewController.h"
#import "LZAuthSendCodeViewController.h"
#import "LZAuthKitAlert.h"
#import "LZAuthKitApi.h"
#import "LZAuthUserInfo.h"

#if __has_include("LZAuthGTOneLoginApp.h")
#import "LZAuthGTOneLoginApp.h"
#import <OneLoginSDK/OneLoginSDK.h>
#endif

#import <AuthenticationServices/AuthenticationServices.h>

@interface LZAuthAucViewController ()<UITableViewDelegate, UITableViewDataSource>
{
    NSMutableArray<NSArray<NSNumber *> *> *_data;
    UITableView *_tableview;
}

@end

@implementation LZAuthAucViewController

- (void)dealloc
{
    NSLog(@"dealloc %@", self.class);
}

- (void)viewDidLoad
{
    [super viewDidLoad];

    self.title = @"鉴权";

    _data = @[
        @[@(LZAuthPlatformPhone)],
        @[
#if __has_include("LZAuthGTOneLoginApp.h")
            @(LZAuthPlatformGTOneLogin),
#endif
#if __has_include("LZAuthQQApp.h")
            @(LZAuthPlatformQQ),
#endif
#if __has_include("LZAuthWeChatApp.h")
            @(LZAuthPlatformWeChat),
#endif
        ],
        @[
#if __has_include("LZAuthFacebookApp.h")
            @(LZAuthPlatformFacebook),
#endif
#if __has_include("LZAuthTikTokApp.h")
                  @(LZAuthPlatformTikTok),
#endif
#if __has_include("LZAuthSnapChatApp.h")
                  @(LZAuthPlatformSnapChat),
#endif
#if __has_include("LZAuthTwitterApp.h")
                  @(LZAuthPlatformTwitter),
#endif
#if __has_include("LZAuthTwitterOAuth2App.h")
                  @(LZAuthPlatformTwitterOAuth2),
#endif
#if __has_include("LZAuthLineApp.h")
                  @(LZAuthPlatformLine),
#endif
        ],
    ].mutableCopy;
    
    #if __has_include("LZAuthAppleApp.h")
        if (@available(iOS 13.0, *)) {
            [_data addObject:@[@(LZAuthPlatformAppleLogin)]];
        }
    #endif

    _tableview = [[UITableView alloc] initWithFrame:self.view.bounds style:UITableViewStyleGrouped];
    _tableview.delegate = self;
    _tableview.dataSource = self;
    _tableview.tableFooterView = [UIView new];
    [self.view addSubview:_tableview];
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return _data.count;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return _data[section].count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:@"LZAKE_LIST_CELL_IDENTIFIER"];
    if (!cell) {
        cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleSubtitle reuseIdentifier:@"LZAKE_LIST_CELL_IDENTIFIER"];
    }
    cell.textLabel.text = [self descriptionWithNetwork:[_data[indexPath.section][indexPath.row] intValue]];
    if ([_data[indexPath.section][indexPath.row] intValue] == LZAuthPlatformPhone) {
        cell.accessoryType = UITableViewCellAccessoryDisclosureIndicator;
    }

    return cell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    return 52;
}

- (NSString *)tableView:(UITableView *)tableView titleForHeaderInSection:(NSInteger)section
{
    switch (section) {
        case 0:
            return @"";
        case 1:
            return @"国内平台";
        case 2:
            return @"国际平台";
        case 3:
            return @"系统平台";
        default:
            return @"其他";
    }
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    [tableView deselectRowAtIndexPath:indexPath animated:YES];

    LZAuthPlatform platform = [_data[indexPath.section][indexPath.row] intValue];
    NSMutableDictionary *userInfo = @{}.mutableCopy;
    if (platform == LZAuthPlatformGoogle ||
        platform == LZAuthPlatformGTOneLogin ||
        platform == LZAuthPlatformAppleLogin ||
        platform == LZAuthPlatformTikTok ||
        platform == LZAuthPlatformSnapChat) {
        userInfo[LZAuthTopViewController] = self;
    }
    if (platform == LZAuthPlatformGTOneLogin) {
#ifdef LZAuthGTOneLoginApp_h
        userInfo[LZAuthGTOneLoginViewModel] = [OLAuthViewModel new];
#endif
    }
    switch (platform) {
        case LZAuthPlatformPhone: // 手机验证码鉴权
        {
            LZAuthSendCodeViewController *vc = [[LZAuthSendCodeViewController alloc] init];
            [self.navigationController pushViewController:vc animated:YES];
            break;
        }
        case LZAuthPlatformGTOneLogin: // 极验一键登录
        case LZAuthPlatformAppleLogin: // Apple登录
        case LZAuthPlatformQQ:
        case LZAuthPlatformFacebook:
        case LZAuthPlatformWeChat:
        case LZAuthPlatformTikTok:
        case LZAuthPlatformTwitter:
        case LZAuthPlatformTwitterOAuth2:
        case LZAuthPlatformLine:
        case LZAuthPlatformSnapChat:
        {
            [LZAuthKit authorizeAccount:platform scope:nil userInfo:userInfo platformHandler:^(LZAuthUserInfo *_Nullable authInfo, NSError *_Nullable error, NSDictionary<NSString *, id> *_Nullable userInfo) {
                
                if (@available(iOS 13.0, *)) {
                    ASAuthorizationAppleIDCredential *credential = authInfo.extraInfo[@"AppleIDCredential"];
                    if ([credential isKindOfClass:[ASAuthorizationAppleIDCredential class]]) {
                        NSLog(@"authorizationCode: %@", [[NSString alloc] initWithData:credential.authorizationCode encoding:NSUTF8StringEncoding]);
                    }
                }
                
                if (error) {
                    [self presentViewController:[LZAuthKitAlert alertWithTitle:@"回调" message:error.description] animated:YES completion:^{
                    }];
                }
            } accountHandler:^(LZAccountResponse *_Nonnull response) {
                [self presentViewController:[LZAuthKitAlert alertWithTitle:@"回调" message:response.description] animated:YES completion:^{
                }];
            }];
            break;
        }
        default:
            break;
    }
}

- (NSString *)descriptionWithNetwork:(LZAuthPlatform)network
{
    switch (network) {
        case LZAuthPlatformQQ:
            return @"QQ鉴权";
        case LZAuthPlatformWeChat:
            return @"Wechat鉴权";
        case LZAuthPlatformFacebook:
            return @"Facebook鉴权";
        case LZAuthPlatformAppleLogin:
            return @"Apple鉴权";
        case LZAuthPlatformGTOneLogin:
            return @"极验鉴权";
        case LZAuthPlatformPhone:
            return @"短信鉴权";
        case LZAuthPlatformTikTok:
            return @"TikTok授权";
        case LZAuthPlatformSnapChat:
            return @"SnapChat授权";
        case LZAuthPlatformTwitter:
            return @"Twitter授权";
        case LZAuthPlatformTwitterOAuth2:
            return @"Twitter-OAuth2.0授权";
        case LZAuthPlatformLine:
            return @"Line授权";
        default:
            return @"未知鉴权";
            break;
    }
}

@end
