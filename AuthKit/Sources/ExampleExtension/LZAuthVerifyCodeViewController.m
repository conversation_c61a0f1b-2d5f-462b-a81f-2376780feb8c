//
//  LZAuthVerifyCodeViewController.m
//  LZAccountKit_Example
//
//  Created by dingyuta<PERSON> on 2020/3/16.
//  Copyright © 2020 dingyutao. All rights reserved.
//

#import "LZAuthVerifyCodeViewController.h"
#import "LZAuthKitApi.h"
#import "LZAuthKitAlert.h"
#import "LZAccountService.h"

@interface LZAuthVerifyCodeViewController ()
{
    UITextField *_codeTextField;
}

@end

@implementation LZAuthVerifyCodeViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = [UIColor whiteColor];
    // Do any additional setup after loading the view.

    UILabel *label = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, 330, 26)];
    label.center = CGPointMake([UIScreen mainScreen].bounds.size.width / 2, 120);
    label.text = @"请输入验证码";
    label.font = [UIFont systemFontOfSize:20];
    [self.view addSubview:label];

    UILabel *label3 = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, 330, 26)];
    label3.center = CGPointMake([UIScreen mainScreen].bounds.size.width / 2, 150);
    label3.text = @"验证码已通过短信发送至 +86 ***********";
    label3.font = [UIFont systemFontOfSize:13];
    label3.textColor = UIColor.lightGrayColor;
    [self.view addSubview:label3];

    _codeTextField = [[UITextField alloc] initWithFrame:CGRectMake(0, 0, 330, 44)];
    _codeTextField.center = CGPointMake([UIScreen mainScreen].bounds.size.width / 2, 200);
    _codeTextField.backgroundColor = UIColor.groupTableViewBackgroundColor;
    _codeTextField.keyboardType = UIKeyboardTypePhonePad;
    _codeTextField.font = [UIFont systemFontOfSize:15];
    _codeTextField.layer.cornerRadius = 2;
    NSMutableDictionary *attrs = [NSMutableDictionary dictionary]; // 创建属性字典
    attrs[NSFontAttributeName] = [UIFont systemFontOfSize:15]; // 设置font
    NSAttributedString *attStr = [[NSAttributedString alloc] initWithString:@"请输入验证码" attributes:attrs];
    _codeTextField.attributedPlaceholder = attStr;

    _codeTextField.leftView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, 20, 0)];
    _codeTextField.leftViewMode = UITextFieldViewModeAlways;

    [self.view addSubview:_codeTextField];

    UIButton *button = [[UIButton alloc] initWithFrame:CGRectMake(0, 0, 330, 44)];
    button.center = CGPointMake([UIScreen mainScreen].bounds.size.width / 2, 285);
    button.titleLabel.textColor = UIColor.whiteColor;
    button.titleLabel.font = [UIFont boldSystemFontOfSize:15];
    [button setTitle:@"登录" forState:UIControlStateNormal];
    button.layer.cornerRadius = 2;
    [button addTarget:self action:@selector(verify) forControlEvents:UIControlEventTouchUpInside];
    button.backgroundColor = UIColor.systemPinkColor;
    [self.view addSubview:button];
}

- (void)verify
{
    if (!_codeTextField.text.length) {
        [self presentViewController:[LZAuthKitAlert alertWithTitle:@"错误" message:@"请输入正确验证码"] animated:YES completion:^{ }];
        return;
    }

    LZAccountInfo *account = [LZAccountInfo new];
    account.network = LZAuthPlatformPhone;
    account.account = _account;
    account.password = _codeTextField.text;
    
//    [LZAuthKit setMushroomEnable:NO];
    [LZAccountService authentication:account completion:^(LZAccountResponse *response) {
//        [LZAuthKit setMushroomEnable:YES];
        [self presentViewController:[LZAuthKitAlert alertWithTitle:response.code == 0 ? @"鉴权成功" : @"鉴权失败" message:@""] animated:YES completion:^{ }];
    }];
}

- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event
{
    [self.view endEditing:YES];
}

@end
