//
//  LZAuthAuzViewController.m
//  AuthKit
//
//  Created by dingyutao on 2020/8/22.
//

#import "LZAuthAuzViewController.h"
#import "LZAuthKitApi.h"
#import "LZAuthKitAlert.h"
#import "LZAuthUserInfo.h"

#if __has_include("LZAuthGTOneLoginApp.h")
#import "LZAuthGTOneLoginApp.h"
#import <OneLoginSDK/OneLoginSDK.h>
#endif

@interface LZAuthAuzViewController ()<UITableViewDataSource, UITableViewDelegate>
{
    NSMutableArray<NSArray<NSNumber *> *> *_data;
    UITableView *_tableview;
}

@end

@implementation LZAuthAuzViewController

- (void)dealloc
{
    NSLog(@"dealloc %@", self.class);
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.title = @"授权";
    self.view.backgroundColor = UIColor.whiteColor;

    _data = @[
                @[
#if __has_include("LZAuthGTOneLoginApp.h")
                @(LZAuthPlatformGTOneLogin),
#endif
#if __has_include("LZAuthQQApp.h")
                @(LZAuthPlatformQQ),
#endif
#if __has_include("LZAuthWeiboApp.h")
              @(LZAuthPlatformWeibo),
#endif
#if __has_include("LZAuthWeChatApp.h")
            @(LZAuthPlatformWeChat),
#endif
                ],
                @[
#if __has_include("LZAuthGoogleApp.h")
                  @(LZAuthPlatformGoogle),
#endif
#if __has_include("LZAuthFacebookApp.h")
                @(LZAuthPlatformFacebook),
#endif
#if __has_include("LZAuthTikTokApp.h")
                  @(LZAuthPlatformTikTok),
#endif
#if __has_include("LZAuthSnapChatApp.h")
                  @(LZAuthPlatformSnapChat),
#endif
#if __has_include("LZAuthTwitterApp.h")
                  @(LZAuthPlatformTwitter),
#endif
#if __has_include("LZAuthTwitterOAuth2App.h")
                  @(LZAuthPlatformTwitterOAuth2),
#endif
#if __has_include("LZAuthLineApp.h")
                  @(LZAuthPlatformLine),
#endif
                ],
    ].mutableCopy;
    
#if __has_include("LZAuthAppleApp.h")
    if (@available(iOS 13.0, *)) {
        [_data addObject:@[@(LZAuthPlatformAppleLogin)]];
    }
#endif

    _tableview = [[UITableView alloc] initWithFrame:self.view.bounds style:UITableViewStyleGrouped];
    _tableview.delegate = self;
    _tableview.dataSource = self;
    _tableview.tableFooterView = [UIView new];
    [self.view addSubview:_tableview];
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return _data.count;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return _data[section].count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:@"LZAKE_LIST_CELL_IDENTIFIER"];
    if (!cell) {
        cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleSubtitle reuseIdentifier:@"LZAKE_LIST_CELL_IDENTIFIER"];
    }
    LZAuthPlatform platform = [_data[indexPath.section][indexPath.row] intValue];
    cell.textLabel.text = [self descriptionWithNetwork:platform];
    cell.detailTextLabel.text = [LZAuthKit isInstalledForPlatform:platform] ? @"install" : @"uninstall";
    return cell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    return 52;
}

- (NSString *)tableView:(UITableView *)tableView titleForHeaderInSection:(NSInteger)section
{
    switch (section) {
        case 0:
            return @"国内平台";
        case 1:
            return @"国际平台";
        case 2:
            return @"系统平台";
        default:
            return @"其他";
    }
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    [tableView deselectRowAtIndexPath:indexPath animated:YES];

    LZAuthPlatform platform = [_data[indexPath.section][indexPath.row] intValue];
    NSMutableDictionary *userInfo = @{}.mutableCopy;
    if (platform == LZAuthPlatformGoogle ||
        platform == LZAuthPlatformGTOneLogin ||
        platform == LZAuthPlatformAppleLogin ||
        platform == LZAuthPlatformTikTok ||
        platform == LZAuthPlatformSnapChat ||
        platform == LZAuthPlatformLine) {
        userInfo[LZAuthTopViewController] = self;
    }
    if (platform == LZAuthPlatformGTOneLogin) {
#ifdef LZAuthGTOneLoginApp_h
        userInfo[LZAuthGTOneLoginViewModel] = [OLAuthViewModel new];
#endif
    }

    NSSet *socpe;
    if (platform == LZAuthPlatformLine) {
        socpe = [NSSet setWithObjects:@"email", @"gender", nil];
    }
    [LZAuthKit authorizePlatform:platform
                           scope:socpe
                   userInfo:userInfo
          completionHandler:^(LZAuthUserInfo *_Nullable authInfo, NSError *_Nullable error, NSDictionary<NSString *, id> *_Nullable userInfo) {
         NSString *info = error ? error.description : authInfo.description;
         [self presentViewController:[LZAuthKitAlert alertWithTitle:@"回调" message:info] animated:YES completion:^{
         }];
     }];
}

- (NSString *)descriptionWithNetwork:(LZAuthPlatform)platform
{
    switch (platform) {
        case LZAuthPlatformQQ:
            return @"QQ授权";
        case LZAuthPlatformWeChat:
            return @"Wechat授权";
        case LZAuthPlatformFacebook:
            return @"Facebook授权";
        case LZAuthPlatformAppleLogin:
            return @"Apple授权";
        case LZAuthPlatformGTOneLogin:
            return @"极验授权";
        case LZAuthPlatformPhone:
            return @"短信授权";
        case LZAuthPlatformGoogle:
            return @"Google授权";
        case LZAuthPlatformWeibo:
            return @"微博授权";
        case LZAuthPlatformTikTok:
            return @"TikTok授权";
        case LZAuthPlatformSnapChat:
            return @"SnapChat授权";
        case LZAuthPlatformTwitter:
            return @"Twitter授权";
        case LZAuthPlatformTwitterOAuth2:
            return @"Twitter-OAuth2.0授权";
        case LZAuthPlatformLine:
            return @"Line授权";
        default:
            return @"未知授权";
            break;
    }
}

@end
