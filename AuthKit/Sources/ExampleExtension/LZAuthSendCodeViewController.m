//
//  LZAuthSendCodeViewController.m
//  LZAccountKit_Example
//
//  Created by dingy<PERSON><PERSON> on 2020/3/16.
//  Copyright © 2020 dingyutao. All rights reserved.
//

#import "LZAuthSendCodeViewController.h"
#import "LZAuthVerifyCodeViewController.h"
#import "LZAuthCountryCodeViewController.h"
#import "LZAuthKitAlert.h"
#import "LZAuthKitApi.h"
#import "LZAccountService.h"
#import "AuthKit-swift.h"

@interface LZAuthSendCodeViewController ()<UITextFieldDelegate>
{
    UITextField *_tf;
    UITextField *_smsTemplateId;
    UITextField *_lang;
    UIButton *_codeBtn;
    NSString *_ccode;
}
@end

@implementation LZAuthSendCodeViewController

- (void)viewDidLoad {
    [super viewDidLoad];

    _ccode = @"86";

    self.view.backgroundColor = UIColor.whiteColor;
    // Do any additional setup after loading the view.

    UILabel *label = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, 330, 26)];
    label.center = CGPointMake([UIScreen mainScreen].bounds.size.width / 2, 120);
    label.text = @"登录后即可展示自己";
    label.font = [UIFont systemFontOfSize:20];
    [self.view addSubview:label];

    UILabel *label3 = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, 330, 26)];
    label3.center = CGPointMake([UIScreen mainScreen].bounds.size.width / 2, 150);
    label3.text = @"登录即表明同意 用户协议 和 隐私政策";
    label3.font = [UIFont systemFontOfSize:13];
    label3.textColor = UIColor.lightGrayColor;
    [self.view addSubview:label3];

    _tf = [[UITextField alloc] initWithFrame:CGRectMake(0, 0, 330, 44)];
    _tf.center = CGPointMake([UIScreen mainScreen].bounds.size.width / 2, 200);
    _tf.backgroundColor = UIColor.groupTableViewBackgroundColor;
    _tf.keyboardType = UIKeyboardTypePhonePad;
    _tf.font = [UIFont systemFontOfSize:15];
    _tf.layer.cornerRadius = 2;
    _tf.layer.masksToBounds = YES;
    NSMutableDictionary *attrs = [NSMutableDictionary dictionary];
    attrs[NSFontAttributeName] = [UIFont systemFontOfSize:15];
    NSAttributedString *attStr = [[NSAttributedString alloc] initWithString:@"请输入手机号" attributes:attrs];
    _tf.attributedPlaceholder = attStr;
    _tf.delegate = self;
//    _tf.text = @"15816523766";
    _tf.text = @"18602026607";

    _codeBtn = [[UIButton alloc] initWithFrame:CGRectMake(0, 0, 56, 44)];
    _codeBtn.backgroundColor = UIColor.groupTableViewBackgroundColor;
    [_codeBtn setTitle:@"   +86   " forState:UIControlStateNormal];
    [_codeBtn setTitleColor:UIColor.blackColor forState:UIControlStateNormal];
    [_codeBtn.titleLabel setFont:[UIFont systemFontOfSize:15]];
    [_codeBtn addTarget:self action:@selector(countryCode) forControlEvents:UIControlEventTouchUpInside];
    _tf.leftView = _codeBtn;
    _tf.leftViewMode = UITextFieldViewModeAlways;
    [self.view addSubview:_tf];
    
    _smsTemplateId = [[UITextField alloc] initWithFrame:CGRectMake(0, 0, 300, 44)];
    _smsTemplateId.center = CGPointMake([UIScreen mainScreen].bounds.size.width / 2, 255);
    _smsTemplateId.backgroundColor = UIColor.groupTableViewBackgroundColor;
    _smsTemplateId.keyboardType = UIKeyboardTypePhonePad;
    _smsTemplateId.font = [UIFont systemFontOfSize:15];
    _smsTemplateId.layer.cornerRadius = 2;
    _smsTemplateId.layer.masksToBounds = YES;
    NSAttributedString *attStrSmsTemplateId = [[NSAttributedString alloc] initWithString:@"请输入模版id 非必填"];
    _smsTemplateId.attributedPlaceholder = attStrSmsTemplateId;
    _smsTemplateId.delegate = self;
    //_smsTemplateId.text = @"非必填";
    _smsTemplateId.text = @"5173836835232686207";
    [self.view addSubview:_smsTemplateId];
    
    _lang = [[UITextField alloc] initWithFrame:CGRectMake(0, 0, 300, 44)];
    _lang.center = CGPointMake([UIScreen mainScreen].bounds.size.width / 2, 310);
    _lang.backgroundColor = UIColor.groupTableViewBackgroundColor;
    _lang.keyboardType = UIKeyboardTypeDefault;
    _lang.font = [UIFont systemFontOfSize:15];
    _lang.layer.cornerRadius = 2;
    _lang.layer.masksToBounds = YES;
    NSAttributedString *attStrLang = [[NSAttributedString alloc] initWithString:@"请输入语言 非必填"];
    _lang.attributedPlaceholder = attStrLang;
    _lang.delegate = self;
    //_lang.text = @"非必填";
    [self.view addSubview:_lang];

    UILabel *label2 = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, 330, 26)];
    label2.center = CGPointMake([UIScreen mainScreen].bounds.size.width / 2, 365);
    label2.text = @"未注册的手机号验证通过后将自动注册";
    label2.font = [UIFont systemFontOfSize:13];
    label2.textColor = UIColor.lightGrayColor;
    [self.view addSubview:label2];

    UIButton *button = [[UIButton alloc] initWithFrame:CGRectMake(0, 0, 330, 44)];
    button.center = CGPointMake([UIScreen mainScreen].bounds.size.width / 2, 425);
    button.titleLabel.textColor = UIColor.whiteColor;
    button.titleLabel.font = [UIFont boldSystemFontOfSize:15];
    button.layer.cornerRadius = 2;
    [button setTitle:@"获取短信验证码" forState:UIControlStateNormal];
    [button addTarget:self action:@selector(getCode) forControlEvents:UIControlEventTouchUpInside];
    button.backgroundColor = UIColor.systemPinkColor;
    [self.view addSubview:button];
    
//    UIButton *button2 = [[UIButton alloc] initWithFrame:CGRectMake(0, 0, 330, 44)];
//    button2.center = CGPointMake([UIScreen mainScreen].bounds.size.width / 2, 479);
//    button2.titleLabel.textColor = UIColor.whiteColor;
//    button2.titleLabel.font = [UIFont boldSystemFontOfSize:15];
//    button2.layer.cornerRadius = 2;
//    [button2 setTitle:@"极验验证后-获取短信验证码" forState:UIControlStateNormal];
//    [button2 addTarget:self action:@selector(getCodeWithGeeTest) forControlEvents:UIControlEventTouchUpInside];
//    button2.backgroundColor = UIColor.systemPinkColor;
//    [self.view addSubview:button2];
}

- (void)countryCode
{
    [LZAccountService queryCountryCode:^(LZAccountResponse *response) {
        LZAuthCountryCodeViewController *vc = [[LZAuthCountryCodeViewController alloc] init];
        NSArray *data = response.data[@"items"];
        vc.datasource = data;
        vc.completionBlock = ^(NSString *_Nonnull code) {
            self->_ccode = [code stringByReplacingOccurrencesOfString:@"+" withString:@""];
            NSString *ccode = [NSString stringWithFormat:@"   %@   ", code];
            [self->_codeBtn setTitle:ccode forState:UIControlStateNormal];
        };
        [self.navigationController pushViewController:vc animated:YES];
    }];
}

- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event
{
    [self.view endEditing:YES];
}

- (void)getCode {
    [self getCode:NO];
}

- (void)getCodeWithGeeTest {
    [self getCode:YES];
}

- (void)getCode:(BOOL)geeTestEnable
{
    if (_tf.text.length != 11) {
        [self presentViewController:[LZAuthKitAlert alertWithTitle:@"错误" message:@"请输入正确手机号"] animated:YES completion:^{ }];
    }

    NSString *account = [NSString stringWithFormat:@"%@-%@", _ccode, _tf.text];
    NSString *smsTemplateId = nil;
    if (_smsTemplateId.text.length>0) {
        smsTemplateId =_smsTemplateId.text;
    }

    NSString *lang = nil;
    if (_lang.text.length>0) {
        lang =_lang.text;
    }

    if (geeTestEnable) {
        [self geeTestVerfiyWithCompletion:^(NSDictionary * _Nullable additionParameters, NSError * _Nullable error) {
            if (error != nil) {
                [self presentViewController:[LZAuthKitAlert alertWithTitle:@"错误" message:error.description] animated:YES completion:^{ }];
                return;
            }
            [self getCode:account smsTemplateId:smsTemplateId lang:lang additionParameters:additionParameters];
        }];
    } else {
        [self getCode:account smsTemplateId:smsTemplateId lang:lang additionParameters:nil];
    }
    //5173836835232686207 5164321726019928703
}

- (void)getCode:(NSString*)account smsTemplateId:(NSString*)smsTemplateId lang:(NSString*)lang additionParameters:(nullable NSDictionary<NSString*, NSString*>*)additionParameters  {
    [LZAccountService sendIdentifyCode:LZAuthIdentifyCodeTypePhone account:account smsTemplateId:smsTemplateId lang:lang additionParameters: additionParameters completion:^(LZAccountResponse *response) {
        if (response.code != 0) {
            if (response.code == 204) {
                [self getCode: true];
            } else {
                [self presentViewController:[LZAuthKitAlert alertWithTitle:@"错误" message:response.description] animated:YES completion:^{ }];
            }
        } else {
            LZAuthVerifyCodeViewController *vc = [[LZAuthVerifyCodeViewController alloc] init];
            vc.account = account;
            [self.navigationController pushViewController:vc animated:YES];
        }
    }];
}

@end
