//
//  LZAuthKitViewController.m
//  AuthKit
//
//  Created by dingyutao on 2020/8/22.
//

#import "LZAuthKitViewController.h"
#import "LZAuthAucViewController.h"
#import "LZAuthAuzViewController.h"

@interface LZAuthKitViewController ()<UITableViewDelegate, UITableViewDataSource>
{
    NSArray<NSString *> *_titleSources;
    NSArray<NSString *> *_descSources;
    UITableView *_tableview;
}

@end

@implementation LZAuthKitViewController

- (void)viewDidLoad {
    [super viewDidLoad];

    self.title = @"AuthKit Example";
    self.view.backgroundColor = UIColor.whiteColor;

    _titleSources = @[@"授权", @"鉴权"];
    _descSources = @[@"拉起第三方平台授权获取信息", @"拉起第三方平台授权获取信息后调服务端接口进行鉴权"];

    _tableview = [[UITableView alloc] initWithFrame:self.view.bounds style:UITableViewStyleGrouped];
    _tableview.delegate = self;
    _tableview.dataSource = self;
    _tableview.tableFooterView = [UIView new];
    [self.view addSubview:_tableview];
}

// MARK: -
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return _titleSources.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:@"LZAKE_LIST_CELL_IDENTIFIER"];
    if (!cell) {
        cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleSubtitle reuseIdentifier:@"LZAKE_LIST_CELL_IDENTIFIER"];
    }
    cell.accessoryType = UITableViewCellAccessoryDisclosureIndicator;

    cell.textLabel.text = _titleSources[indexPath.row];
    cell.detailTextLabel.text = _descSources[indexPath.row];
    return cell;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    [tableView deselectRowAtIndexPath:indexPath animated:YES];

    if (indexPath.row == 0) {
        [self.navigationController pushViewController:[[LZAuthAuzViewController alloc] init] animated:YES];
    } else {
        [self.navigationController pushViewController:[[LZAuthAucViewController alloc] init] animated:YES];
    }
}

@end
