//
//  LZAuthCountryCodeViewController.m
//  LZAccountKit_Example
//
//  Created by dingyutao on 2020/3/17.
//  Copyright © 2020 dingyutao. All rights reserved.
//

#import "LZAuthCountryCodeViewController.h"

@interface LZAuthCountryCodeViewController ()<UITableViewDelegate, UITableViewDataSource>
{
    UITableView *_tableview;
}
@end

@implementation LZAuthCountryCodeViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.

    self.view.backgroundColor = UIColor.whiteColor;

    _tableview = [[UITableView alloc] initWithFrame:self.view.frame];
    _tableview.delegate = self;
    _tableview.dataSource = self;
    [self.view addSubview:_tableview];
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return _datasource.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    UITableViewCell *cell = [[UITableViewCell alloc] init];
    NSDictionary *data = _datasource[indexPath.row];
    cell.textLabel.text = [NSString stringWithFormat:@"%@  %@", data[@"title"], data[@"code"]];

    return cell;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    if (_completionBlock) {
        _completionBlock(_datasource[indexPath.row][@"code"]);
    }
    [self.navigationController popViewControllerAnimated:YES];
}

@end
