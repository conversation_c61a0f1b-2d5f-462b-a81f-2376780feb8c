//
//  LZAuthSendCodeViewController+.swift
//  AuthKit
//
//  Created by ellzu on 2022/12/14.
//

import Foundation
import GeeTestAuth

extension LZAuthSendCodeViewController {
    
    @objc
    public func geeTestVerfiy(completion: @escaping ([AnyHashable: Any]?, Error?)->Void) {
        let configuration = GTAuth.Configuration(geetestID: "2ca3dd3ca5d46123f3249310af9961d7")
//        let configuration = GTAuth.Configuration.default
        GTAuth(configuration).verify { result in
            switch result {
            case .success(let parameters):
                completion(parameters, nil)
            case .failure(let error):
                completion(nil, error)
            }
        }
    }
}
