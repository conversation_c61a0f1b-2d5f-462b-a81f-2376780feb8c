//
//  LZAuthKitAlert.m
//  AuthKit
//
//  Created by dingyutao on 2020/8/22.
//

#import "LZAuthKitAlert.h"

@implementation LZAuthKitAlert

+ (UIAlertController *)alertWithTitle:(NSString *)title message:(NSString *)message
{
    UIAlertController *alert = [UIAlertController alertControllerWithTitle:title message:message preferredStyle:UIAlertControllerStyleAlert];
    UIAlertAction *cancel = [UIAlertAction actionWithTitle:@"确定" style:UIAlertActionStyleDefault handler:^(UIAlertAction *_Nonnull action) {
    }];
    [alert addAction:cancel];
    return alert;
}

@end
