//
//  LZAuthTwitterApp.m
//  AuthKit
//
//  Created by sam on 2022/5/23.
//

#import "LZAuthTwitterApp.h"
#import "LZAuthKitDefine.h"
#import "LZAuthUserInfo.h"
#import <TwitterKit/TWTRKit.h>

@interface LZAuthTwitterApp ()

@property (nonatomic,copy) LZAuthCompletionHandler completionHandler;

@end

@implementation LZAuthTwitterApp

+ (instancetype)_sharedInstance {
	static id instance = nil;
	static dispatch_once_t onceToken;
	dispatch_once(&onceToken, ^{
		instance = [[self alloc] init];
	});
	return instance;
}

+(void)authWithHandler:(LZAuthCompletionHandler)completionHandler userInfo:(NSDictionary *)userInfo scope:(NSSet<NSString *> *__nullable)scope {
	[LZAuthTwitterApp authWithHandler:completionHandler scope:scope];
}

+(void)authWithHandler:(LZAuthCompletionHandler)completionHandler scope:(NSSet<NSString *> *__nullable)scope {
	[LZAuthTwitterApp _sharedInstance].completionHandler = completionHandler;
	__weak typeof(self) weakself = self;
	[[Twitter sharedInstance] logInWithCompletion:^(TWTRSession * _Nullable session, NSError * _Nullable error) {
        if(session) {
			 [weakself authSuccess:session];
        }else {
			 [weakself authFail:error];
        }
    }];
}

+ (nonnull instancetype)applicationHandler {
	return [self _sharedInstance];
}

/// 验证成功
/// @param session 信息
+(void)authSuccess:(TWTRSession *)session {
    
    LZAuthUserInfo *userInfo = [[LZAuthUserInfo alloc] init];
    userInfo.platformId = LZAuthPlatformTwitter;
    userInfo.openId = session.authToken;
    userInfo.token = session.authTokenSecret;
    userInfo.name = session.userName;
    
    dispatch_async(dispatch_get_main_queue(), ^{
       if ([LZAuthTwitterApp _sharedInstance].completionHandler) {
           [LZAuthTwitterApp _sharedInstance].completionHandler(userInfo,nil,nil);
       }
    });
}

/// 验证失败
/// @param error 错误原因
+(void)authFail:(NSError *)error {
	LZAuthStatusCode code = LZAuthStatusCodeFail;
	if(error.code == TWTRLogInErrorCodeCancelled) {
		code = LZAuthStatusCodeCancel;
	}
	NSError *err = [NSError errorWithDomain:LZAuthErrorDomain
	                code:code
	                userInfo:@{
	                        NSLocalizedDescriptionKey : [NSString stringWithFormat:@"Twitter Auth: %@", error.description]
	}];

	[LZAuthTwitterApp _sharedInstance].completionHandler(nil,err,nil);
}

@end
