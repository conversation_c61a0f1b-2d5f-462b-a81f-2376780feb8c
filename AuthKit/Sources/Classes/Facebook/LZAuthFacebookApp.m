//
//  LZAuthFacebookApp.m
//  AuthKit
//
//  Created by dingyutao on 2020/8/21.
//

#import "LZAuthFacebookApp.h"
#import "LZAuthKitDefine.h"
#import "LZAuthUserInfo.h"
@import FBSDKLoginKit;

@interface LZAuthFacebookApp ()

@property (nonatomic, copy) LZAuthCompletionHandler completionHandler;
@property (nonatomic, assign) int protaritWH;

@end

@implementation LZAuthFacebookApp

+ (void)authWithHandler:(LZAuthCompletionHandler)completionHandler userInfo:(NSDictionary *)userInfo scope:(NSSet<NSString *> *__nullable)scope
{
    if (userInfo) {
        [LZAuthFacebookApp _sharedInstance].protaritWH = [userInfo[LZAuthPortraitWH] intValue];
    }

    [self authWithHandler:completionHandler scope:scope];
}

+ (void)authWithHandler:(LZAuthCompletionHandler)completionHandler scope:(NSSet<NSString *> *__nullable)scope
{
    [LZAuthFacebookApp _sharedInstance].completionHandler = completionHandler;
    FBSDKLoginManager *login = [[FBSDKLoginManager alloc] init];
    [login logOut];
    if (!scope) {
        scope = [NSSet setWithArray:@[kLZAuthFacebookPublicProfile, kLZAuthFacebookUserAgeRange, kLZAuthFacebookUserGender]];
    }
    [login logInWithPermissions:[scope allObjects]
             fromViewController:nil
                        handler:^(FBSDKLoginManagerLoginResult *_Nullable result, NSError *_Nullable error) {
                            if (result.token) {
                                [[LZAuthFacebookApp _sharedInstance] _loadUserInfoWithResult:result];
                            } else if (result.isCancelled) {
                                NSError *error = [NSError errorWithDomain:LZAuthErrorDomain
                                                                     code:LZAuthStatusCodeCancel
                                                                 userInfo:@{ NSLocalizedDescriptionKey: [NSString stringWithFormat:@"Facebook Auth Cancel"] }];
                                [[LZAuthFacebookApp _sharedInstance] _authFailWithError:error];
                            } else if (error) {
                                [[LZAuthFacebookApp _sharedInstance] _authFailWithError:error];
                            } else {
                                NSError *error = [NSError errorWithDomain:LZAuthErrorDomain
                                                                     code:LZAuthStatusCodeFail
                                                                 userInfo:@{ NSLocalizedDescriptionKey: [NSString stringWithFormat:@"Facebook Auth Fail"] }];
                                [[LZAuthFacebookApp _sharedInstance] _authFailWithError:error];
                            }
                        }];
}

+ (nonnull id)applicationHandler {
    return [LZAuthFacebookApp _sharedInstance];
}

// MARK: - Private Method

+ (instancetype)_sharedInstance {
    static id instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[self alloc] init];
    });
    return instance;
}

- (void)_loadUserInfoWithResult:(FBSDKLoginManagerLoginResult *)result
{
    NSString *token = result.token.tokenString;

    int protaritWH = _protaritWH > 0 ? _protaritWH : 720; // 默认720*720
    NSString *prarm = [NSString stringWithFormat:@"id, name, gender, age_range, picture.width(%d).height(%d)",protaritWH,protaritWH];

    NSDictionary *params = @{ @"fields": prarm };
    FBSDKGraphRequest *request = [[FBSDKGraphRequest alloc] initWithGraphPath:result.token.userID parameters:params HTTPMethod:@"GET"];
    [request startWithCompletion:^(id<FBSDKGraphRequestConnecting>  _Nullable connection, id  _Nullable result, NSError * _Nullable error) {
        if ([result isKindOfClass:NSDictionary.class]) {
            [self _authSuccessWithAuthInfo:result token:token];
        } else {
            NSError *err = [NSError errorWithDomain:LZAuthErrorDomain
                                               code:LZAuthStatusCodeReqUserError
                                           userInfo:@{ NSLocalizedDescriptionKey: error.description }];
            [self _authFailWithError:err];
        }
    }];

}

- (void)_authSuccessWithAuthInfo:(NSDictionary *)authInfo token:(NSString *)token
{
    if (_completionHandler != nil) {
        LZAuthUserInfo *userInfo = [[LZAuthUserInfo alloc] init];
        userInfo.platformId = LZAuthPlatformFacebook;
        userInfo.openId = authInfo[@"id"];
        userInfo.token = token;
        userInfo.name = authInfo[@"name"];
        userInfo.cover = [self _parseUserCoverWithAuthInfo:authInfo];
        userInfo.gender = -1;
        NSString *gender = authInfo[@"gender"];
        if (gender && [gender isKindOfClass: NSString.class]) {
            if ([gender isEqualToString:@"female"]) {
                userInfo.gender = 1;
            }else if ([gender isEqualToString:@"male"]) {
                userInfo.gender = 0;
            }
        }
        userInfo.extraInfo = authInfo;
        _completionHandler(userInfo, nil, nil);
        _completionHandler = nil;
    }
}

- (void)_authFailWithError:(NSError *)error
{
    if (_completionHandler != nil) {
        NSMutableDictionary *userInfo = [[NSMutableDictionary alloc] init];
        
        if (error) {
            userInfo[LZAuthPlatformErrorKey] = error;
            if(![error.domain isEqualToString:LZAuthErrorDomain]) {
                userInfo[NSLocalizedDescriptionKey] = error.description;
            }
        }
        
        NSError *err = [NSError errorWithDomain:LZAuthErrorDomain
                                    code:error.code==2?LZAuthStatusCodeCancel:LZAuthStatusCodeFail
                                userInfo:userInfo];
        
        _completionHandler(nil, err, nil);
        _completionHandler = nil;
    }
}

- (NSString *)_parseUserCoverWithAuthInfo:(NSDictionary *)authInfo
{
    id picture = authInfo[@"picture"];
    if ([picture isKindOfClass:[NSDictionary class]]) {
        id data = (NSDictionary *)picture[@"data"];
        if ([data isKindOfClass:[NSDictionary class]]) {
            return data[@"url"];
        }
    }
    return @"";
}

@end
