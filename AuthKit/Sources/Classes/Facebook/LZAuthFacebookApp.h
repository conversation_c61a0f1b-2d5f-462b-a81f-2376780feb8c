//
//  LZAuthFacebookApp.h
//  AuthKit
//
//  Created by dingyuta<PERSON> on 2020/8/21.
//

#import <Foundation/Foundation.h>
#import "LZAuthProtocol.h"

#ifndef LZAuthFacebookApp_h
#define LZAuthFacebookApp_h

#define kLZAuthFacebookPublicProfile @"public_profile"
#define kLZAuthFacebookUserAgeRange @"user_age_range"
#define kLZAuthFacebookUserGender @"user_gender"

NS_ASSUME_NONNULL_BEGIN

@interface LZAuthFacebookApp : NSObject <LZAuthPlatormable>

@end

NS_ASSUME_NONNULL_END

#endif
