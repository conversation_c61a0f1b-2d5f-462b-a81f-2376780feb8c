//
//  LZAuthUserInfo.m
//  AuthKit
//
//  Created by dingyuta<PERSON> on 2020/8/21.
//

#import "LZAuthUserInfo.h"

@implementation LZAuthUserInfo

- (BOOL)isValid {
    return (_platformId > 0 && _openId != nil && _token != nil);
}

- (NSString *)infoString {
    return [NSString stringWithFormat:@"platformId:%lu,openId:%@,token:%@,openKey:%@,authTime:%lld,expiresTime:%lld,refreshToken:%@,name:%@,cover:%@,gender:%ld,unionId:%@,extraInfo:%@", (unsigned long)_platformId, _openId, _token, _openKey, _authTime, _expiresTime, _refreshToken, _name, _cover, (long)_gender, _unionId, _extraInfo];
}

- (NSString *)description {
    return [self infoString];
}

@end
