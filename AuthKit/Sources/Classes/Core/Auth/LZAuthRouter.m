//
//  LZAuthRouter.m
//  AuthKit
//
//  Created by dingyuta<PERSON> on 2020/8/21.
//

#import "LZAuthRouter.h"
#import "LZAuthUserInfo.h"
#import "LZAuthInternalDefine.h"

#if __has_include("LZAuthQQApp.h")
#import "LZAuthQQApp.h"
#endif
#if __has_include("LZAuthQQUser.h")
#import "LZAuthQQUser.h"
#endif

#if __has_include("LZAuthWeChatApp.h")
#import "LZAuthWeChatApp.h"
#endif
#if __has_include("LZAuthWeChatUser.h")
#import "LZAuthWeChatUser.h"
#endif

#if __has_include("LZAuthWeiboApp.h")
#import "LZAuthWeiboApp.h"
#endif
#if __has_include("LZAuthWeiboUser.h")
#import "LZAuthWeiboUser.h"
#endif

#if __has_include("LZAuthFacebookApp.h")
#import "LZAuthFacebookApp.h"
#endif
#if __has_include("LZAuthFacebookUser.h")
#import "LZAuthFacebookUser.h"
#endif

#if __has_include("LZAuthGoogleApp.h")
#import "LZAuthGoogleApp.h"
#endif
#if __has_include("LZAuthGoogleUser.h")
#import "LZAuthGoogleUser.h"
#endif

#if __has_include("LZAuthAppleApp.h")
#import "LZAuthAppleApp.h"
#endif
#if __has_include("LZAuthAppleUser.h")
#import "LZAuthAppleUser.h"
#endif

#if __has_include("LZAuthGTOneLoginApp.h")
#import "LZAuthGTOneLoginApp.h"
#endif

#if __has_include("LZAuthTikTokApp.h")
#import "LZAuthTikTokApp.h"
#endif

#if __has_include("LZAuthSnapChatApp.h")
#import "LZAuthSnapChatApp.h"
#endif

#if __has_include("LZAuthTikTokUser.h")
#import "LZAuthTikTokUser.h"
#endif

#if __has_include("LZAuthTwitterApp.h")
#import "LZAuthTwitterApp.h"
#endif

#if __has_include("LZAuthTwitterOAuth2App.h")
#import "LZAuthTwitterOAuth2App.h"
#endif

#if __has_include("LZAuthLineApp.h")
#import "LZAuthLineApp.h"
#endif


@implementation LZAuthRouter

+ (void) authPlatform:(LZAuthPlatform)platform
                scope:(NSSet<NSString *> *__nullable)scope
             userInfo:(NSDictionary <NSString *, id> *_Nullable)userInfo
    completionHandler:(LZAuthCompletionHandler _Nonnull)completionHandler {
    NSParameterAssert(completionHandler);
    if (!completionHandler) {
        return;
    }

    LZAuthCompletionHandler handler = ^(LZAuthUserInfo *_Nullable authInfo, NSError *_Nullable error, NSDictionary<NSString *, id> *_Nullable userInfo) {
        if (error) {
            LogI(@"auth completion with error: %@", error.description);
            dispatch_async(dispatch_get_main_queue(), ^{
                LogI(@"callback auth error.");
                completionHandler(nil, error, nil);
            });
            return;
        }
        LogI(@"auth completion with authInfo.");
        authInfo.authTime = (int64_t)(([[NSDate date] timeIntervalSince1970] - 120) * 1000);
        [self _requestUserInfoWithAuthInfo:authInfo
                         completionHandler:completionHandler];
    };

    [self _authFromPlatform:platform
                      scope:scope
                    handler:handler
                   userInfo:userInfo];
}

// MARK: - Private Method

+ (void)_authFromPlatform:(LZAuthPlatform)platform
                    scope:(NSSet<NSString *> *__nullable)scope
                  handler:(LZAuthCompletionHandler)handler
                 userInfo:(NSDictionary *)userInfo {
    switch (platform) {
        case LZAuthPlatformQQ: {
#if defined(LZAuthQQApp_h)
            [LZAuthQQApp authWithHandler:handler scope:scope];
#else
            NSString *reason = @"LZAuthKit: Please insert `LZAuthKit/QQ` into you `Podfile`";
            [self  _failForReason:reason
                completionHandler:handler];
#endif
            break;
        }
        case LZAuthPlatformWeChat: {
#if defined(LZAuthWeChatApp_h)
            [LZAuthWeChatApp authWithHandler:handler scope:scope];
#else
            NSString *reason = @"LZAuthKit: Please insert `LZAuthKit/WeChat` into you `Podfile`";
            [self  _failForReason:reason
                completionHandler:handler];
#endif
            break;
        }
        case LZAuthPlatformWeibo: {
#if defined(LZAuthWeiboApp_h)

            [LZAuthWeiboApp authWithHandler:handler scope:scope];
#else
            NSString *reason = @"LZAuthKit: Please insert `LZAuthKit/Weibo` into you `Podfile`";
            [self  _failForReason:reason
                completionHandler:handler];
#endif
            break;
        }
        case LZAuthPlatformFacebook: {
#if defined(LZAuthFacebookApp_h)
            if (userInfo) {
                [LZAuthFacebookApp authWithHandler:handler userInfo:userInfo scope:scope];
            } else {
                [LZAuthFacebookApp authWithHandler:handler scope:scope];
            }
#else
            NSString *reason = @"LZAuthKit: Please insert `LZAuthKit/Facebook` into you `Podfile`";
            [self  _failForReason:reason
                completionHandler:handler];
#endif
            break;
        }
        case LZAuthPlatformGoogle: {
#if defined(LZAuthGoogleApp_h)
            if (userInfo) {
                [LZAuthGoogleApp authWithHandler:handler userInfo:userInfo scope:scope];
            } else {
                [LZAuthGoogleApp authWithHandler:handler scope:scope];
            }
#else
            NSString *reason = @"LZAuthKit: Please insert `LZAuthKit/Google` into you `Podfile`";
            [self  _failForReason:reason
                completionHandler:handler];
#endif
            break;
        }
        case LZAuthPlatformAppleLogin: {
#if defined(LZAuthAppleApp_h)
            if (@available(iOS 13.0, *)) {
                if (userInfo) {
                    [LZAuthAppleApp authWithHandler:handler userInfo:userInfo];
                } else {
                    [LZAuthAppleApp authWithHandler:handler];
                }
            } else {
                NSString *reason = @"LZAuthKit: Sign in with Apple need iOS 13 or above";
                [self  _failForReason:reason
                    completionHandler:handler];
            }
#else
            NSString *reason = @"LZAuthKit: Please insert `LZAuthKit/Apple` into you `Podfile`";
            [self  _failForReason:reason
                completionHandler:handler];
#endif
            break;
        }
        case LZAuthPlatformGTOneLogin: {
#if defined(LZAuthGTOneLoginApp_h)
            if (userInfo) {
                [LZAuthGTOneLoginApp authWithHandler:handler userInfo:userInfo];
            } else {
                NSString *reason = @"LZAuthKit: GTOneLogin need parameter TopViewController in userInfo";
                [self  _failForReason:reason
                    completionHandler:handler];
            }
#endif
            break;
        }
        case LZAuthPlatformTikTok: {
#ifdef LZAuthTikTokApp_h
            if (userInfo) {
                [LZAuthTikTokApp authWithHandler:handler userInfo:userInfo scope:scope];
            } else {
                NSString *reason = @"LZAuthKit: TikTok need parameter TopViewController in userInfo";
                [self  _failForReason:reason
                    completionHandler:handler];
            }
#endif
            break;
        }
        case LZAuthPlatformSnapChat: {
#ifdef LZAuthSnapChatApp_h
            if (userInfo) {
                [LZAuthSnapChatApp authWithHandler:handler userInfo:userInfo scope:scope];
            } else {
                NSString *reason = @"LZAuthKit: SnapChat need parameter TopViewController in userInfo";
                [self  _failForReason:reason
                    completionHandler:handler];
            }
#endif
            break;
        }
        case LZAuthPlatformTwitter: {
#ifdef LZAuthTwitterApp_h
                [LZAuthTwitterApp authWithHandler:handler userInfo:userInfo scope:scope];
#endif
            break;
        }
        case LZAuthPlatformTwitterOAuth2: {
#ifdef LZAuthTwitterOAuth2App_h
            [LZAuthTwitterOAuth2App authWithHandler:handler userInfo:userInfo scope:scope];
#endif
            break;
        }
        case LZAuthPlatformLine: {
#ifdef LZAuthLineApp_h
            [LZAuthLineApp authWithHandler:handler userInfo:userInfo scope:scope];
#endif
            break;
        }
        default: {
            NSString *reason = [NSString stringWithFormat:@"LZAuthKit: Please input correct `LZAuthPlatform`, NOT for %lu", (unsigned long)platform];
            [self  _failForReason:reason
                completionHandler:handler];
            break;
        }
    }
}

+ (void)_requestUserInfoWithAuthInfo:(LZAuthUserInfo *_Nonnull)authInfo
                   completionHandler:(LZAuthCompletionHandler)completionHandler {
    NSParameterAssert(authInfo);
    if (!authInfo) {
        NSError *err = [NSError errorWithDomain:LZAuthErrorDomain
                                           code:LZAuthStatusCodeReqUserError
                                       userInfo:@{
                            NSLocalizedDescriptionKey: [NSString stringWithFormat:@"%s: %d Request User: LZAuthUserInfo is nil", __FILE__, __LINE__]
        }];
        LogE(@"request userInfo error:%@", err.description);
        dispatch_async(dispatch_get_main_queue(), ^{
            LogE(@"callback error");
            completionHandler(nil, err, nil);
        });
        return;
    }
    if (authInfo.authCode) {
        dispatch_async(dispatch_get_main_queue(), ^{
            LogE(@"callback code");
            completionHandler(authInfo, nil, nil);
        });
        return;
    }

    NSURL *url = [self _userInfoURLWithAuthInfo:authInfo];
    if (!url) {
        dispatch_async(dispatch_get_main_queue(), ^{
            LogE(@"callback authInfo");
            completionHandler(authInfo, nil, nil);
        });
        return;
    }

    [[[NSURLSession sharedSession] dataTaskWithURL:url completionHandler:^(NSData *_Nullable data, NSURLResponse *_Nullable response, NSError *_Nullable error) {
        if (error) {
            NSError *err = [NSError errorWithDomain:LZAuthErrorDomain
                                               code:LZAuthStatusCodeReqUserError
                                           userInfo:@{
                                NSLocalizedDescriptionKey: [NSString stringWithFormat:@"%s: %d Request User: %@", __FILE__, __LINE__, error.description]
            }];
            dispatch_async(dispatch_get_main_queue(), ^{
                               completionHandler(nil, err, nil);
                           });
            return;
        }

        NSError *err;
        [self _parseUserInfoData:data
                      toAuthInfo:authInfo
                           error:&err];

        if (err) {
            dispatch_async(dispatch_get_main_queue(), ^{
                               completionHandler(nil, err, nil);
                           });
        }

        dispatch_async(dispatch_get_main_queue(), ^{
                           completionHandler(authInfo, nil, nil);
                       });
    }] resume];
}

+ (NSURL *_Nullable)_userInfoURLWithAuthInfo:(LZAuthUserInfo *)authInfo {
    NSURL *url = nil;
    switch (authInfo.platformId) {
        case LZAuthPlatformQQ:
#ifdef LZAuthQQApp_h
            url = [LZAuthQQUser userInfoApiWithAuthInfo:authInfo];
#else
            NSAssert(NO, @"LZAuthKit: Please insert `LZAuthKit/QQ` into you `Podfile`");
#endif
            break;
        case LZAuthPlatformWeChat:
#ifdef LZAuthWeChatApp_h
            url = [LZAuthWeChatUser userInfoApiWithAuthInfo:authInfo];
#else
            NSAssert(NO, @"LZAuthKit: Please insert `LZAuthKit/WeChat` into you `Podfile`");
#endif
            break;
        case LZAuthPlatformWeibo:
#ifdef LZAuthWeiboApp_h
            url = [LZAuthWeiboUser userInfoApiWithAuthInfo:authInfo];
#else
            NSAssert(NO, @"LZAuthKit: Please insert `LZAuthKit/Weibo` into you `Podfile`");
#endif
            break;
        case LZAuthPlatformFacebook:
        case LZAuthPlatformGoogle:
        case LZAuthPlatformGTOneLogin:
        case LZAuthPlatformAppleLogin:
        case LZAuthPlatformSnapChat:
        case LZAuthPlatformTwitter:
        case LZAuthPlatformTwitterOAuth2:
        case LZAuthPlatformLine:
            break;
        case LZAuthPlatformTikTok:
#ifdef LZAuthTikTokApp_h
            url = [LZAuthTikTokUser userInfoApiWithAuthInfo:authInfo];
#else
            NSAssert(NO, @"LZAuthKit: Please insert `LZAuthKit/TikTok` into you `Podfile`");
#endif
            break;
        default:
            NSAssert(NO, @"LZAuthKit: Please input correct `LZAuthPlatform`, NOT for %lu", (unsigned long)authInfo.platformId);
            break;
    }
    return url;
}

+ (void)_parseUserInfoData:(NSData *)data
                toAuthInfo:(LZAuthUserInfo *)authInfo
                     error:(NSError **)error {
    switch (authInfo.platformId) {
        case LZAuthPlatformQQ:
#ifdef LZAuthQQApp_h
            [LZAuthQQUser parseUserInfoData:data
                                 toAuthInfo:authInfo
                                      error:error];
#else
            NSAssert(NO, @"LZAuthKit: Please insert `LZAuthKit/QQ` into you `Podfile`");
#endif
            break;
        case LZAuthPlatformWeChat:
#ifdef LZAuthWeChatApp_h
            [LZAuthWeChatUser parseUserInfoData:data
                                     toAuthInfo:authInfo
                                          error:error];
#else
            NSAssert(NO, @"LZAuthKit: Please insert `LZAuthKit/WeChat` into you `Podfile`");
#endif
            break;
        case LZAuthPlatformWeibo:
#ifdef LZAuthWeiboApp_h
            [LZAuthWeiboUser parseUserInfoData:data
                                    toAuthInfo:authInfo
                                         error:error];
#else
            NSAssert(NO, @"LZAuthKit: Please insert `LZAuthKit/Weibo` into you `Podfile`");
#endif
            break;
        case LZAuthPlatformFacebook:
        case LZAuthPlatformGoogle:
        case LZAuthPlatformAppleLogin:
        case LZAuthPlatformSnapChat:
        case LZAuthPlatformTwitter:
        case LZAuthPlatformTwitterOAuth2:
        case LZAuthPlatformLine:
            break;
        case LZAuthPlatformTikTok:
#ifdef LZAuthTikTokApp_h
            [LZAuthTikTokUser parseUserInfoData:data
                                     toAuthInfo:authInfo
                                          error:error];
#else
            NSAssert(NO, @"LZAuthKit: Please insert `LZAuthKit/TikTok` into you `Podfile`");
#endif
            break;
        default:
            NSAssert(NO, @"LZAuthKit: Please input correct `LZAuthPlatform`, NOT for %lu", (unsigned long)authInfo.platformId);
            break;
    }
}

+ (void)_failForReason:(NSString *)reason
     completionHandler:(LZAuthCompletionHandler _Nonnull)completionHandler {
    NSAssert(NO, reason);
    NSError *err = [NSError errorWithDomain:LZAuthErrorDomain
                                       code:LZAuthStatusCodeFail
                                   userInfo:@{
                        NSLocalizedDescriptionKey: reason ? : @""
    }];
    completionHandler(nil, err, nil);
}

@end
