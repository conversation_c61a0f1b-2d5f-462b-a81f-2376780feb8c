//
//  LZAuthRouter.h
//  AuthKit
//
//  Created by dingy<PERSON><PERSON> on 2020/8/21.
//

#import <Foundation/Foundation.h>
#import "LZAuthKitDefine.h"

#ifndef LZAuthRouter_h
#define LZAuthRouter_h

NS_ASSUME_NONNULL_BEGIN

/// 第三方授权路由
@interface LZAuthRouter : NSObject

+ (void)authPlatform:(LZAuthPlatform)platform scope:(NSSet<NSString *> *__nullable)scope userInfo:(NSDictionary <NSString *, id> * _Nullable)userInfo completionHandler:(LZAuthCompletionHandler _Nonnull)completionHandler;

@end

NS_ASSUME_NONNULL_END

#endif
