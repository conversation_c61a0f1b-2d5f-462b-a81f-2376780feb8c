//
//  LZAuthUserInfo.h
//  AuthKit
//
//  Created by dingyuta<PERSON> on 2020/8/21.
//

#import <Foundation/Foundation.h>
#import "LZAuthKitDefine.h"

NS_ASSUME_NONNULL_BEGIN

@interface LZAuthUserInfo : NSObject

@property (nonatomic, assign) LZAuthPlatform platformId;
@property (nonatomic, copy) NSString *openId;
@property (nonatomic, copy) NSString *token;
@property (nonatomic, copy) NSString *authCode;
@property (nonatomic, copy) NSString *codeVerifier;
@property (nonatomic, copy) NSString *openKey;
@property (nonatomic, assign) int64_t authTime;
@property (nonatomic, assign) int64_t expiresTime;
@property (nonatomic, copy) NSString *refreshToken;
@property (nonatomic, copy) NSString *name;
@property (nonatomic, copy) NSString *cover;
@property (nonatomic, assign) NSInteger gender;

@property (nonatomic, copy) NSDictionary *extraInfo;    /// 平台回调源数据

/// 目前只有微信有
@property (nonatomic, copy) NSString *unionId;

- (BOOL)isValid;
- (NSString *)infoString;

@end

static NSString* LZAuthUserInfoExtraInfoTikTokAuthCode = @"tikTokAuthCode"; //tiktok SDK TikTokOpenSDKAuthResponse.code

NS_ASSUME_NONNULL_END
