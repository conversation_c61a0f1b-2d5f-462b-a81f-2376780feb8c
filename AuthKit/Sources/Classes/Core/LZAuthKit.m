//
//  LZAuthKit.m
//  LZAuthKit
//
//  Created by dingyutao on 2020/3/13.
//

#import "LZAuthKit.h"
#import "LZAuthKitDefine.h"
#import "LZAuthKitReport.h"
#import "LZAuthRouter.h"
#import "LZAuthUserInfo.h"
#import "AuthKit-swift.h"

#if __has_include("LZAccountInfo.h")
#import "LZAccountInfo.h"
#endif

#if __has_include("LZAccountService.h")
#import <AuthKit/LZAccountService.h>
#endif

#if __has_include("LZAuthQQApp.h")
#import <AuthKit/LZAuthQQApp.h>
#endif

#if __has_include("LZAuthWeChatApp.h")
#import <AuthKit/LZAuthWeChatApp.h>
#endif

#if __has_include("LZAuthWeiboApp.h")
#import <AuthKit/LZAuthWeiboApp.h>
#endif

#if __has_include("LZAuthFacebookApp.h")
#import <AuthKit/LZAuthFacebookApp.h>
#endif

#if __has_include("LZAuthGoogleApp.h")
#import <AuthKit/LZAuthGoogleApp.h>
#endif

#if __has_include("LZAuthAppleApp.h")
#import <AuthKit/LZAuthAppleApp.h>
#endif


#if __has_include("LZAuthSendCodeViewController.h")
#import <AuthKit/LZAuthSendCodeViewController.h>
#endif

@import social_base;
@import BaseTool;

static NSString *_appId = @"";
static NSString *_subAppId = @"0";
static NSString *_serverURL = @"";
static NSString *_deviceId = @"";
static BOOL _mushroomEnable = YES;

@implementation LZAuthKit

+ (void)startupWithAppId:(NSString *)appId deviceId:(NSString *)deviceId
{
    [LZAuthKit setAppId:appId];
    [LZAuthKit setDeviceId:deviceId];
    
    IComponent *component = IEnvironments.standard[@"AUTH"];
    [LZAuthKit setServerURL:component[@"serverHost"]];
    
    id mushroomEnable = component[@"mushroom.enable"];
    if ([mushroomEnable isKindOfClass:NSNumber.class]) {
        [LZAuthKit setMushroomEnable: ((NSNumber*)mushroomEnable).boolValue ];
    }
    [MushroomSupport didStartup];
    
    [LZAuthKit registPlatforms:^(Class<LZAuthKitRegisterProtocol>  _Nonnull __unsafe_unretained platformRegister) {
        
#if __has_include("LZAuthQQApp.h")
        [platformRegister registPlatform:LZAuthPlatformQQ
                                  appKey:component[@"qq.appId"]
                               appSecret:nil
                             redirectURL:nil
                                userInfo:@{ LZAuthUniversalLink: component[@"qq.ulink"] }];
#endif
#if __has_include("LZAuthWeChatApp.h")
        [platformRegister registPlatform:LZAuthPlatformWeChat
                                  appKey:component[@"wechat.appId"]
                               appSecret:nil
                             redirectURL:nil
                                userInfo:@{ LZAuthUniversalLink: component[@"wechat.ulink"] }];
#endif
#if __has_include("LZAuthWeiboApp.h")
        [platformRegister registPlatform:LZAuthPlatformWeibo
                                  appKey:component[@"weibo.appKey"]
                               appSecret:nil
                             redirectURL:component[@"weibo.redirectURL"]
                                userInfo:@{ LZAuthUniversalLink: component[@"weibo.ulink"] }];
#endif
#if __has_include("LZAuthFacebookApp.h")
        // FacebookSDK只支持从Info.plist读取AppKey
        // 因此，这里就传nil了。
        [platformRegister registPlatform:LZAuthPlatformFacebook
                                  appKey:nil
                               appSecret:nil
                             redirectURL:nil
                                userInfo:nil];
#endif
#if __has_include("LZAuthGoogleApp.h")
        [platformRegister registPlatform:LZAuthPlatformGoogle
                                  appKey:component[@"google.clientId"]
                               appSecret:nil
                             redirectURL:nil
                                userInfo:nil];
#endif
#if __has_include("LZAuthGTOneLoginApp.h")
        [platformRegister registPlatform:LZAuthPlatformGTOneLogin
                                  appKey:component[@"gtOneLogin.appId"]
                               appSecret:nil
                             redirectURL:nil
                                userInfo:nil];
#endif
#if __has_include("LZAuthSnapChatApp.h")
        [platformRegister registPlatform:LZAuthPlatformSnapChat
                                  appKey:nil
                               appSecret:nil
                             redirectURL:nil
                                userInfo:nil];
#endif
#if __has_include("LZAuthTikTokApp.h")
        [platformRegister registPlatform:LZAuthPlatformTikTok
                                  appKey:component[@"tiktok.appKey"]
                               appSecret:nil
                             redirectURL:component[@"tiktok.redirectURL"]
                                userInfo:nil];
#endif
#if __has_include("LZAuthTwitterApp.h")
        [platformRegister registPlatform:LZAuthPlatformTwitter
                                  appKey:component[@"twitter.appKey"]
                               appSecret:component[@"twitter.appSecret"]
                             redirectURL:nil
                                userInfo:nil];
#endif
#if __has_include("LZAuthTwitterOAuth2App.h")
        [platformRegister registPlatform:LZAuthPlatformTwitterOAuth2
                                  appKey:component[@"twitter2.clientId"]
                               appSecret:nil
                             redirectURL:component[@"twitter2.redirectURL"]
                                userInfo:nil];
#endif
#if __has_include("LZAuthLineApp.h")
        [platformRegister registPlatform:LZAuthPlatformLine
                                  appKey:component[@"line.channelID"]
                               appSecret:nil
                             redirectURL:nil
                                userInfo:nil];
#endif
    }];

}

+ (void)registPlatforms:(void (^)(Class<LZAuthKitRegisterProtocol> platformRegister))registHandler {
    NSAssert(registHandler, @"%s: %d: registHandler should NOT be nil", __FILE__, __LINE__);
    if (!registHandler) {
        return;
    }
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        registHandler(LZAuthKitRegister.class);
    });
}

+ (void)setServerURL:(NSString *)serverURL
{
    _serverURL = serverURL;
}

+ (NSString *)serverURL {
    return _serverURL;
}

+ (void)setAppId:(NSString *)appId
{
    _appId = appId;
}

+ (NSString *)appId
{
    return _appId;
}

+ (void)setSubAppId:(NSString *)subAppId
{
    _subAppId = subAppId;
}

+ (NSString *)subAppId
{
    return _subAppId;
}

+ (void)setDeviceId:(NSString *)deviceId
{
    _deviceId = deviceId;
}

+ (NSString *)deviceId
{
    return _deviceId;
}

// MARK: -
/// 判断当前平台是否已安装
+ (BOOL)isInstalledForPlatform:(LZAuthPlatform)platform
{
    if (platform == LZAuthPlatformAppleLogin) {
#ifdef LZAuthAppleApp_h
        return YES;
#else
        return NO;
#endif
    }

    return [LZSocial connector:[self _socialPlatform:platform]].isInstalled;
}

+ (BOOL)isInitializeForPlatform:(LZAuthPlatform)platform
{
    if (platform == LZAuthPlatformAppleLogin) {
#ifdef LZAuthAppleApp_h
        return YES;
#else
        return NO;
#endif
    }

    return [LZSocial connector:[self _socialPlatform:platform]].isInitialize;
}

+ (void)authorizePlatform:(LZAuthPlatform)platform
                 userInfo:(NSDictionary <NSString *, id> *_Nullable)userInfo
        completionHandler:(LZAuthCompletionHandler _Nonnull)completionHandler {
    
    [self authorizePlatform:platform scope:nil userInfo:userInfo completionHandler:completionHandler];
}

/// 调用第三方平台鉴权
+ (void)authorizePlatform:(LZAuthPlatform)platform
                    scope:(NSSet<NSString *> * __nullable)scope
                 userInfo:(NSDictionary <NSString *, id> *_Nullable)userInfo
        completionHandler:(LZAuthCompletionHandler _Nonnull)completionHandler {
    NSParameterAssert(completionHandler);
    if (!completionHandler) {
        return;
    }

    LZAuthCompletionHandler RDSCompletionHandler = [LZAuthKitReport handlerAuthPlatform:platform completionHandler:completionHandler];

    BOOL hasClient = [self isInstalledForPlatform:platform];

    // 检查业务处理是否需要客户端, 如果需要,但未安装客户端, 则直接返回 error
    // 如果不用检查,则直接调用 SDK 进行鉴权
    // 默认为不检查
    NSNumber *checkClientNum = userInfo[LZAuthCheckHasClient];
    BOOL shouldCheckClient = checkClientNum.boolValue;

    if (shouldCheckClient && !hasClient) {
        NSError *err = [NSError errorWithDomain:LZAuthErrorDomain
                                           code:LZAuthStatusCodeHasNoClient
                                       userInfo:@{
                            NSLocalizedDescriptionKey: [NSString stringWithFormat:@"LZAuthKit: Auth: Has not install platform %lu", (unsigned long)platform]
        }];
        RDSCompletionHandler(nil, err, nil);
        return;
    }

    if (![self isInitializeForPlatform:platform]) {
        NSError *err = [NSError errorWithDomain:LZAuthErrorDomain
                                           code:LZAuthStatusCodeNotInitialize
                                       userInfo:@{
                            NSLocalizedDescriptionKey: [NSString stringWithFormat:@"LZAuthKit: Auth: Has not initialize platform %lu", (unsigned long)platform]
        }];
        RDSCompletionHandler(nil, err, nil);
        return;
    }

    [LZAuthRouter authPlatform:platform
                         scope:scope
                      userInfo:userInfo
             completionHandler:RDSCompletionHandler];
}

+ (void)authorizeAccount:(LZAuthPlatform)platform
                userInfo:(NSDictionary <NSString *, id> * _Nullable)userInfo
         platformHandler:(LZAuthCompletionHandler _Nullable)platformHandler
          accountHandler:(LZAuthServerCompletion _Nonnull)accountHandler
{
    [self authorizeAccount:platform scope:nil userInfo:userInfo platformHandler:platformHandler accountHandler:accountHandler];
}

+ (void)authorizeAccount:(LZAuthPlatform)platform
                   scope:(NSSet<NSString *> * __nullable)scope
                userInfo:(NSDictionary<NSString *, id> *)userInfo
         platformHandler:(LZAuthCompletionHandler)platformHandler
          accountHandler:(LZAuthServerCompletion)accountHandler
{
#ifdef LZAccountService_h
    [self authorizePlatform:platform scope:scope userInfo:userInfo completionHandler:^(LZAuthUserInfo *_Nullable authInfo, NSError *_Nullable error, NSDictionary<NSString *, id> *_Nullable userInfo) {
        if (platformHandler) {
            platformHandler(authInfo, error, userInfo);
        }
        
        LZAccountInfo *info = nil;
        
        if (authInfo) {
            info = [LZAccountInfo new];
            info.network = platform;
            info.password = authInfo.token;
            info.account = authInfo.openId;
            info.authCode = authInfo.openKey;
            info.plat = [LZAccountPlatInfo new];
            info.plat.openId = authInfo.openId;
            info.plat.token = authInfo.token;
            info.plat.unionId = authInfo.unionId;
            info.plat.nickname = authInfo.name;
            info.plat.gender = (int32_t)authInfo.gender;
            info.plat.extraInfo = authInfo.extraInfo;
        }
        if (authInfo.authCode) {
            if (!info) {
                info = [LZAccountInfo new];
            }
            info.account = authInfo.authCode;
            if (platform == LZAuthPlatformTikTok) {
                info.password = authInfo.codeVerifier;
            }
        }
        if (info) {
            [LZAccountService authentication:info completion:accountHandler];
        }
        
    }];
#else
    NSAssert(NO, @"AuthKit: Please insert `AuthKit/Authentication` in your `Podfile` ");
#endif
}

+ (BOOL)  application:(UIApplication *)application
              openURL:(NSURL *)url
    sourceApplication:(NSString *)sourceApplication
           annotation:(id)annotation {
    return [LZSocial application:application open:url sourceApplication:sourceApplication annotation:annotation];
}

+ (BOOL)application:(UIApplication *)app openURL:(NSURL *)url options:(NSDictionary<UIApplicationOpenURLOptionsKey, id> *)options
{
    return [LZSocial application:app open:url options:options];
}

+ (BOOL)application:(UIApplication *)application
       userActivity:(NSUserActivity *)userActivity
            handler:(void (^)(NSArray<id<UIUserActivityRestoring> > *_Nullable))restorationHandler
{
    return [LZSocial application:application continue:userActivity restorationHandler:restorationHandler];
}

// MARK: - Private Mehtod

+ (LZSocialPlatform)_socialPlatform:(LZAuthPlatform)platform
{
    switch (platform) {
        case LZAuthPlatformQQ:
            return LZSocialPlatformQQ;
        case LZAuthPlatformWeChat:
            return LZSocialPlatformWeChat;
        case LZAuthPlatformWeibo:
            return LZSocialPlatformWeibo;
        case LZAuthPlatformFacebook:
            return LZSocialPlatformFacebook;
        case LZAuthPlatformGoogle:
            return LZSocialPlatformGoogle;
        case LZAuthPlatformGTOneLogin:
            return LZSocialPlatformGTOneLogin;
        case LZAuthPlatformTikTok:
            return LZSocialPlatformTikTok;
        case LZAuthPlatformSnapChat:
            return LZSocialPlatformSnapChat;
        case LZAuthPlatformTwitter:
            return LZSocialPlatformTwiiter;
        case LZAuthPlatformTwitterOAuth2:
            return LZSocialPlatformTwitterOAuth2;
        case LZAuthPlatformLine:
            return LZSocialPlatformLine;
        default:
            NSAssert(NO, @"LZAuthKit: Please make sure the `LZAuthPlatform` support in social-base!!!");
            return NO;
    }
}

+ (void)setMushroomEnable:(BOOL)mushroomEnable {
    _mushroomEnable = mushroomEnable;
}

+ (BOOL)mushroomEnable {
    return _mushroomEnable;
}


static NSString *_mushroomKeysUpdateURL = @"";
+ (void)setMushroomKeysUpdateURL:(NSString *)mushroomKeysUpdateURL {
    _mushroomKeysUpdateURL = mushroomKeysUpdateURL;
}

+ (NSString*)mushroomKeysUpdateURL {
    return _mushroomKeysUpdateURL;
}

@end
