//
//  MushroomSupport.swift
//  AuthKit
//
//  Created by <PERSON><PERSON><PERSON> on 2022/12/12.
//

import Foundation
import Mushroom
import BaseTool

@objc
public class MushroomSupport: NSObject {
    @objc
    static public  func didStartup() {
        let _ = self.shared
    }
}


extension MushroomSupport {
    public static let shared: Mushroom  = {
        let component = Environments.standard["AUTH"]
        let appID = component?["mushroom.appID"] as? String ?? LZAuthKit.appId()
        let sporesURL = component?["mushroom.serverHost"] as? String ?? LZAuthKit.mushroomKeysUpdateURL
        let mushroom = Mushroom(MushroomConfiguration(appID: appID, tenant: "AuthKit", sporesURL: sporesURL, algoType: .capAndIncrease))
//        let mushroom = Mushroom(MushroomConfiguration(appID: appID, keysURL: keysURL, algoType: .signOnly))
        return mushroom
    }()
    
}
