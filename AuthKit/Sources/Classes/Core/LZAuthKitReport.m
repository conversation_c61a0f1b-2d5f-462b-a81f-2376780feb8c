//
//  LZAuthKitReport.m
//  AuthKit
//
//  Created by dingy<PERSON><PERSON> on 2020/8/22.
//

#import "LZAuthKitReport.h"
#import "LZAuthUserInfo.h"
#import "LZUniqueIdentifierMgr.h"
#import "LZAuthInternalDefine.h"

@import BaseTool;

typedef NS_ENUM (NSUInteger, LZAuthReportStatus) {
    LZAuthReportStatusStart = 1,
    LZAuthReportStatusSuccess,
    LZAuthReportStatusFail,
};

@implementation LZAuthKitReport

+ (void)_event:(NSString *)event label:(NSDictionary *)label
{
    [ITracker trackEvent:event label:label];
}

+ (void)_rdsReportAuthWithTransactionId:(int64_t)transactionId platform:(NSUInteger)platform status:(int32_t)status errMsg:(NSString *)errMsg
{
    @try {
        NSDictionary *label = @{
            @"transactionId": @(transactionId),
            @"platform": @(platform),
            @"status": @(status),
            @"errMsg": errMsg.length ? errMsg : @"",
        };
        [self _event:@"EVENT_SUPPORT_SHARE_AUTH" label:label];
    } @catch (NSException *exception) {
        NSDictionary *label = @{
            @"eventId": @"EVENT_SUPPORT_SHARE_AUTH",
            @"errMsg": exception.reason
        };
        [self _event:@"EVENT_ERROR_EVENT_EXPECTION" label:label];
    }
}

// MARK: - Report

+ (LZAuthCompletionHandler)handlerAuthPlatform:(NSUInteger)platform completionHandler:(LZAuthCompletionHandler)completionHandler
{
    int64_t transactionId = [LZUniqueIdentifierMgr uniqueId];
    [self _rdsReportAuthWithTransactionId:transactionId platform:platform status:LZAuthReportStatusStart errMsg:nil];
    LZAuthCompletionHandler RDSCompletionHandler = nil;
    RDSCompletionHandler = ^(LZAuthUserInfo *_Nullable authInfo, NSError *_Nullable error, NSDictionary<NSString *, id> *_Nullable userInfo) {
        LogI(@"callback in report completionHandler wrapper.");
        if (error) {
            [self _rdsReportAuthWithTransactionId:transactionId platform:platform status:LZAuthReportStatusFail errMsg:error.description];
        } else {
            [self _rdsReportAuthWithTransactionId:transactionId platform:platform status:LZAuthReportStatusSuccess errMsg:nil];
        }
        // 执行原授权回调
        completionHandler(authInfo, error, userInfo);
    };
    return RDSCompletionHandler;
}

@end
