//
//  LZAuthKit.h
//  LZAuthKit
//
//  Created by dingyutao on 2020/3/13.
//

#import <Foundation/Foundation.h>
#import "LZAuthKitDefine.h"
#import "LZAuthKitRegister.h"

NS_ASSUME_NONNULL_BEGIN

@interface LZAuthKit : NSObject

/// 初始化接口，需要在主工程内置 auth.env 组件配置文件
/// @param appId 应用元数据appId
/// @param deviceId 设备Id
+ (void)startupWithAppId:(NSString *)appId deviceId:(NSString *)deviceId;

+ (void)registPlatforms:(void (^) (Class<LZAuthKitRegisterProtocol> platformRegister))registHandler;

/// 设置服务地址，用于切换环境
//#if DEBUG
//        _mgr.serverURL = @"https://accountauthpre.lizhifm.com";
//#else
//        _mgr.serverURL = @"https://accountauth.lizhifm.com";
//#endif
/// @param serverURL 服务地址
+ (void)setServerURL:(NSString *)serverURL;
+ (NSString *)serverURL;

/// 设备id
+ (void)setDeviceId:(NSString *)deviceId;
+ (NSString *)deviceId;

/// 设置 appId ，http 协议请求头需要带
/// @param appId 应用对应的 appId ，见 http://lzstack.lizhi.fm/doc/客户端/应用工厂/应用接入中台清单.html
+ (void)setAppId:(NSString *)appId;
+ (NSString *)appId;

/// 设置 subAppId ，http 协议请求头需要带
/// @param subAppId 应用对应的 subAppId ，见 http://lzstack.lizhi.fm/doc/客户端/应用工厂/应用接入中台清单.html
+ (void)setSubAppId:(NSString *)subAppId;
+ (NSString *)subAppId;

/// 判断当前平台是否已安装
+ (BOOL)isInstalledForPlatform:(LZAuthPlatform)platform;

/// 调用第三方平台授权
/// @param platform 第三方授权平台
/// @param userInfo 附加信息（如 Google iOS10 / 系统自带分享 必传当前控制器 Google Facebook 授权返回头像尺寸）
/// @param completionHandler 授权结果回调
+ (void)authorizePlatform:(LZAuthPlatform)platform
                 userInfo:(NSDictionary <NSString *, id> * _Nullable)userInfo
        completionHandler:(LZAuthCompletionHandler _Nonnull)completionHandler;


/// 调用第三方平台授权（新增scope参数）
/// @param platform 第三方授权平台
/// @param scope 权限控制
/// @param userInfo 附加信息（如 Google iOS10 / 系统自带分享 必传当前控制器 Google Facebook 授权返回头像尺寸）
/// @param completionHandler 授权结果回调
+ (void)authorizePlatform:(LZAuthPlatform)platform
                    scope:(NSSet<NSString *> * __nullable)scope
                 userInfo:(NSDictionary <NSString *, id> *_Nullable)userInfo
        completionHandler:(LZAuthCompletionHandler _Nonnull)completionHandler;

/// 调用第三方平台授权后进行鉴权
/// @param platform 第三方授权平台
/// @param userInfo 附加信息（如 Google iOS10 / 系统自带分享 必传当前控制器 Google Facebook 授权返回头像尺寸
/// @param platformHandler 授权回调（当授权成功时，调用服务端接口进行账号鉴权，授权失败时，不进行鉴权）
/// @param accountHandler 鉴权回调，将鉴权结果返回
+ (void)authorizeAccount:(LZAuthPlatform)platform
                userInfo:(NSDictionary <NSString *, id> * _Nullable)userInfo
         platformHandler:(LZAuthCompletionHandler _Nullable)platformHandler
          accountHandler:(LZAuthServerCompletion _Nonnull)accountHandler;

/// 调用第三方平台授权后进行鉴权（新增scope参数）
/// @param platform 第三方授权平台
/// @param scope 权限控制
/// @param userInfo 附加信息（如 Google iOS10 / 系统自带分享 必传当前控制器 Google Facebook 授权返回头像尺寸）
/// @param platformHandler 授权回调（当授权成功时，调用服务端接口进行账号鉴权，授权失败时，不进行鉴权）
/// @param accountHandler 鉴权回调，将鉴权结果返回
+ (void)authorizeAccount:(LZAuthPlatform)platform
                   scope:(NSSet<NSString *> * __nullable)scope
                userInfo:(NSDictionary <NSString *, id> * _Nullable)userInfo
         platformHandler:(LZAuthCompletionHandler _Nullable)platformHandler
          accountHandler:(LZAuthServerCompletion _Nonnull)accountHandler;

/// 第三方平台返回 app 时处理方法
/// 请在  AppDelegate 的openURL 方法调用
+ (BOOL)application:(UIApplication *)application
            openURL:(NSURL *)url
  sourceApplication:(NSString *)sourceApplication
         annotation:(id)annotation;

/// > iOS9 回调处理
+ (BOOL)application:(UIApplication *)app openURL:(NSURL *)url options:(NSDictionary<UIApplicationOpenURLOptionsKey,id> *)options;

/// UniversalLink 回调处理
+ (BOOL)application:(UIApplication *)application
       userActivity:(NSUserActivity *)userActivity
            handler:(void(^)(NSArray<id<UIUserActivityRestoring>> * __nullable restorableObjects))restorationHandler;

@property (nonatomic, class, assign) BOOL mushroomEnable;
@property (nonatomic, class, copy) NSString* mushroomKeysUpdateURL;

@end

NS_ASSUME_NONNULL_END
