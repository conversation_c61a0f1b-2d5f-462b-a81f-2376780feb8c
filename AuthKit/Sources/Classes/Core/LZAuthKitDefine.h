//
//  LZAuthKitDefine.h
//  LZAuthKit
//
//  Created by dingyutao on 2020/3/16.
//

@class LZAuthUserInfo;
@class LZAccountResponse;

#ifndef LZAuthKitDefine_h
#define LZAuthKitDefine_h

typedef NS_ENUM (NSInteger, LZAuthPlatform) {
    LZAuthPlatformWeibo      = 1,                        /// 微博 授权
    LZAuthPlatformPhone      = 20,                       /// 手机号 验证码
    LZAuthPlatformWeChat     = 51,                       /// 微信 授权
    LZAuthPlatformQQ         = 24,                       /// QQ 授权
    LZAuthPlatformTikTok     = 29,
    LZAuthPlatformSnapChat   = 30,
    LZAuthPlatformGTOneLogin = 36,                       /// 极验一键登录
    LZAuthPlatformGoogle     = 44,                       /// Google 授权
    LZAuthPlatformFacebook   = 45,                       /// Facebook 授权
    LZAuthPlatformAppleLogin = 47,                       /// Apple 授权
    LZAuthPlatformLine = 48,                             /// Line 授权
    LZAuthPlatformTwitter = 49,                     /// 推特 授权
    LZAuthPlatformTwitterOAuth2 = 50,               /// 推特 OAuth2.0 授权
};

typedef NS_ENUM (NSInteger, LZAuthIdentifyCodeType) {
    LZAuthIdentifyCodeTypePhone = 1,                     /// 手机号 短信验证码
    LZAuthIdentifyCodeTypeEmail = 2,                     /// 邮箱 邮件验证码
};

/// 调用服务端接口回调
typedef void (^LZAuthServerCompletion) (LZAccountResponse *_Nonnull response);

/// 第三方鉴权回调 block
typedef void (^LZAuthCompletionHandler) (LZAuthUserInfo *_Nullable authInfo, NSError *_Nullable error, NSDictionary<NSString *, id> *_Nullable userInfo);

/// 第三方鉴权状态码
typedef NS_ENUM (NSInteger, LZAuthStatusCode) {
    LZAuthStatusCodeSuccess,
    LZAuthStatusCodeFail,
    LZAuthStatusCodeCancel,
    LZAuthStatusCodeNetworkError,                                               // 网络异常
    LZAuthStatusCodeHasNoClient,                                                // 未安装app
    LZAuthStatusCodeReqTokenError,                                              // 请求 token 信息出错
    LZAuthStatusCodeReqUserError,                                               // 请求 user 信息出错
    LZAuthStatusCodeNotInitialize,                                              // 未初始化SDK
    LZAuthStatusCodeUnsupportOSVersion                                          // 不支持的系统版本
};

static NSString *_Nonnull LZAuthErrorDomain = @"LZAuthErrorDomain";
static NSString *_Nonnull LZAuthPlatformErrorKey = @"LZAuthPlatformErrorKey";

static NSString *_Nonnull LZAuthUniversalLink = @"LZAuthUniversalLink";         /// Wechat/QQ 初始化必传 Ulink
static NSString *_Nonnull LZAuthTopViewController = @"LZAuthTopViewController"; /// Google iOS10 / 系统自带分享 必传当前控制器
static NSString *_Nonnull LZAuthPortraitWH = @"LZAuthPortraitWH";               /// Google Facebook 授权返回头像尺寸
static NSString *_Nonnull LZAuthCheckHasClient = @"LZAuthCheckHasClient";
static NSString *_Nonnull LZAuthGTOneLoginViewModel = @"LZAuthGTOneLoginViewModel"; /// 极验一键登录视图模型

#endif /* LZAuthKitDefine_h */
