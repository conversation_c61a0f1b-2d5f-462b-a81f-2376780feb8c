//
//  LZAuthProtocol.h
//  AuthKit
//
//  Created by dingyuta<PERSON> on 2020/8/22.
//

#import <Foundation/Foundation.h>
#import "LZAuthKitDefine.h"

#ifndef LZAuthProtocol_h
#define LZAuthProtocol_h

NS_ASSUME_NONNULL_BEGIN

/// 第三方用户协议
@protocol LZAuthUserable <NSObject>

/// 请求用户信息api
+ (NSURL * _Nullable)userInfoApiWithAuthInfo:(LZAuthUserInfo *)authInfo;

/// 解析用户信息
+ (void)parseUserInfoData:(NSData *)data
               toAuthInfo:(LZAuthUserInfo *)authInfo
                    error:(NSError **)error;

@end

/// 第三方平台协议
@protocol LZAuthPlatormable <NSObject>

+ (instancetype)applicationHandler;

+ (void)authWithHandler:(LZAuthCompletionHandler)completionHandler scope:(NSSet<NSString *> *__nullable)scope;

+ (void)authWithHandler:(LZAuthCompletionHandler)completionHandler userInfo:(NSDictionary *)userInfo scope:(NSSet<NSString *> *__nullable)scope;

@end

NS_ASSUME_NONNULL_END

#endif /* LZAuthProtocol_h */
