//
//  LZAuthInternalDefine.h
//  AuthKit
//
//  Created by dingyutao on 2020/8/22.
//

#ifndef LZSKInternalDefine_h
#define LZSKInternalDefine_h

#import <BaseTool/ILog.h>

#define TAG @"AuthKit"
#define LogE(frmt, ...)   ILogTagE(TAG, frmt, ##__VA_ARGS__)
#define LogW(frmt, ...)   ILogTagW(TAG, frmt, ##__VA_ARGS__)
#define LogI(frmt, ...)   ILogTagI(TAG, frmt, ##__VA_ARGS__)
#define LogD(frmt, ...)   ILogTagD(TAG, frmt, ##__VA_ARGS__)

#endif /* LZSKInternalDefine_h */
