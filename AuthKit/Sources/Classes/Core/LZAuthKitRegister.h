//
//  LZAuthKitRegister.h
//  AuthKit
//
//  Created by dingyuta<PERSON> on 2020/8/21.
//

#import <Foundation/Foundation.h>
#import "LZAuthKitDefine.h"

NS_ASSUME_NONNULL_BEGIN

@protocol LZAuthKitRegisterProtocol <NSObject>

/// 注册第三方平台
+ (void)registPlatform:(LZAuthPlatform)platform
                appKey:(NSString * _Nullable)appKey
             appSecret:(NSString * _Nullable)appSecret
           redirectURL:(NSString * _Nullable)redirectURL
              userInfo:(NSDictionary<NSString *, id> * _Nullable)userInfo;

@end

@interface LZAuthKitRegister : NSObject <LZAuthKitRegisterProtocol>

@end

NS_ASSUME_NONNULL_END
