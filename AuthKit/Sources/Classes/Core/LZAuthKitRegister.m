//
//  LZAuthKitRegister.m
//  AuthKit
//
//  Created by dingyuta<PERSON> on 2020/8/21.
//

#import "LZAuthKitRegister.h"

#if __has_include("LZAuthQQApp.h")
#import <AuthKit/LZAuthQQApp.h>
#endif

#if __has_include("LZAuthWeChatApp.h")
#import <AuthKit/LZAuthWeChatApp.h>
#endif

#if __has_include("LZAuthWeiboApp.h")
#import <AuthKit/LZAuthWeiboApp.h>
#endif

#if __has_include("LZAuthFacebookApp.h")
#import <AuthKit/LZAuthFacebookApp.h>
#endif

#if __has_include("LZAuthGoogleApp.h")
#import <AuthKit/LZAuthGoogleApp.h>
#endif

#if __has_include("LZAuthSnapChatApp.h")
#import <AuthKit/LZAuthSnapChatApp.h>
#endif

#if __has_include("LZAuthAppleApp.h")
#import <AuthKit/LZAuthAppleApp.h>
#endif

@import social_base;

@implementation LZAuthKitRegister

+ (void)registPlatform:(LZAuthPlatform)platform
                appKey:(NSString *_Nullable)appKey
             appSecret:(NSString *_Nullable)appSecret
           redirectURL:(NSString *_Nullable)redirectURL
              userInfo:(NSDictionary<NSString *, id> *_Nullable)userInfo
{
    LZSocialPlatform splatform;

    switch (platform) {
        case LZAuthPlatformQQ:
            splatform = LZSocialPlatformQQ;
    #ifdef LZAuthQQApp_h
            [[LZSocial connector:splatform] addApplicationHandler:[LZAuthQQApp applicationHandler]];
    #endif
            break;
        case LZAuthPlatformWeChat:
            splatform = LZSocialPlatformWeChat;
    #ifdef LZAuthWeChatApp_h
            [[LZSocial connector:splatform] addApplicationHandler:[LZAuthWeChatApp applicationHandler]];
    #endif
            break;
        case LZAuthPlatformWeibo:
            splatform = LZSocialPlatformWeibo;
    #ifdef LZAuthWeiboApp_h
            [[LZSocial connector:splatform] addApplicationHandler:[LZAuthWeiboApp applicationHandler]];
    #endif
            break;
        case LZAuthPlatformGoogle:
            splatform = LZSocialPlatformGoogle;
    #ifdef LZAuthGoogleApp_h
            [[LZSocial connector:splatform] addApplicationHandler:[LZAuthGoogleApp applicationHandler]];
    #endif
            break;
        case LZAuthPlatformGTOneLogin:
            splatform = LZSocialPlatformGTOneLogin;
            break;
        case LZAuthPlatformFacebook:
            splatform = LZSocialPlatformFacebook;
            break;
        case LZAuthPlatformAppleLogin:
            splatform = LZSocialPlatformApple;
            break;
        case LZAuthPlatformTikTok:
            splatform = LZSocialPlatformTikTok;
            break;
        case LZAuthPlatformSnapChat:
            splatform = LZSocialPlatformSnapChat;
#ifdef LZAuthSnapChatApp_h
            [[LZSocial connector:splatform] addApplicationHandler:[LZAuthSnapChatApp applicationHandler]];
#endif
            break;
        case LZAuthPlatformTwitter:
            splatform = LZSocialPlatformTwiiter;
            break;
        case LZAuthPlatformTwitterOAuth2:
            splatform = LZSocialPlatformTwitterOAuth2;
            break;
        case LZAuthPlatformLine:
            splatform = LZSocialPlatformLine;
            break;
        default:
            NSAssert(NO, @"LZAuthKit: Please make sure the `LZSKPlatform` support in social-base!!!");
            return;
    }
    if (platform != LZAuthPlatformTwitter) {
        NSAssert(appSecret == nil, @"LZAuthKit: Only TwitterOAuth1 can use appSecret!!!");
    }
    [LZSocial registerPlatform:splatform appId:appKey appSecret:appSecret redirectURL:redirectURL ulink:userInfo[LZAuthUniversalLink] userInfo:userInfo];
}

@end
