//
//  LZAuthGTOneLoginApp.m
//  AuthKit
//
//  Created by dingyutao on 2020/8/22.
//

#import "LZAuthGTOneLoginApp.h"
#import "LZAuthUserInfo.h"

@import social_base;

@implementation LZAuthGTOneLoginApp

+ (void)authWithHandler:(LZAuthCompletionHandler)completionHandler userInfo:(NSDictionary *)userInfo
{
    __weak UIViewController *topViewController = userInfo[LZAuthTopViewController];
    OLAuthViewModel *viewModel = userInfo[LZAuthGTOneLoginViewModel];
    if (!viewModel) {
        viewModel = [OLAuthViewModel new];
    }
    [OneLoginPro requestTokenWithViewController:topViewController viewModel:viewModel completion:^(NSDictionary *_Nullable result) {
        if ([result[@"errorCode"] intValue] != 0) {
            NSError *error = [NSError errorWithDomain:LZAuthErrorDomain
                                                 code:[result[@"errorCode"] intValue]
                                             userInfo:@{ NSLocalizedDescriptionKey: result.description }];
            completionHandler(nil, error, nil);
            return;
        }

        [OneLoginPro dismissAuthViewController:YES completion:^{
            LZAuthUserInfo *userInfo = [[LZAuthUserInfo alloc] init];
            userInfo.platformId = LZAuthPlatformGTOneLogin;
            userInfo.openId = result[@"processID"];
            userInfo.token = result[@"token"];
            userInfo.openKey = result[@"authcode"];
            completionHandler(userInfo, nil, nil);
        }];
    }];
}

@end
