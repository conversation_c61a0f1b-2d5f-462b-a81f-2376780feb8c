//
//  LZAccountService+.swift
//  AuthKit
//
//  Created by el<PERSON><PERSON> on 2022/12/9.
//

import Foundation
import Mushroom

@objc
public class LZAccountHTTP: NSObject {
    
    @objc
    public static func send(_ urlRequest: URLRequest, mushroomEnable: Bool, completion:@escaping (Data?, URLResponse?, Error?)->Void) {
        if mushroomEnable {
            switch MushroomSupport.shared.increase(urlRequest) {
            case .success(let request):
                var request = request
                request.setValue("application/json", forHTTPHeaderField: "Content-Type")
                _send(encryptRequest: request, completion: completion)
                break
            case .failure(let error):
                DispatchQueue.main.async {
                    completion(nil, nil, error)
                }
                break
            }
        } else {
            _send(request: urlRequest, completion: completion);
        }
    }
    
    class func _send(request: URLRequest, completion:@escaping (Data?, URLResponse?, Error?)->Void) {
        let task = URLSession.shared.dataTask(with: request) { data, response, error in
            guard let data = data, let response = response else {
                completion(data, response, error)
                return
            }
            completion(data, response, error)
        }
        task.resume()
    }
    
    class func _send(encryptRequest: URLRequest, completion:@escaping (Data?, URLResponse?, Error?)->Void) {
        let task = URLSession.shared.dataTask(with: encryptRequest) { data, response, error in
            guard let data = data, let response = response else {
                completion(data, response, error)
                return
            }
            switch MushroomSupport.shared.reduce(response: response, data: data) {
            case .success(let package):
                completion(package.data, response, error)
                break
            case .failure(let error):
                completion(nil, response, error)
                break
            }
        }
        task.resume()
    }
}
