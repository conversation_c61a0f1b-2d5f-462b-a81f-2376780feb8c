//
//  LZAccountInfo.h
//  LZAuthKit
//
//  Created by dingyutao on 2020/3/13.
//

#import <Foundation/Foundation.h>

#import "LZAuthKitDefine.h"

NS_ASSUME_NONNULL_BEGIN

/// 第三方授权信息
@interface LZAccountPlatInfo : NSObject

/// 第三方平台用户 openId
@property (nonatomic, copy) NSString *openId;

/// 第三方平台用户 token
@property (nonatomic, copy) NSString *token;

/// 微信平台的 unionId
@property (nonatomic, copy) NSString *unionId;

/// 第三方平台用户昵称
@property (nonatomic, copy) NSString *nickname;

/// 第三方平台的头像（ url 地址）
@property (nonatomic, copy) NSString *portrait;

/// 第三方平台的性别（ 0 表示男， 1 表示女， -1 表示未知）
@property (nonatomic, assign) int32_t gender;

/// token 超期时长（单位秒）
@property (nonatomic, assign) int32_t expiresTime;

/// 绑定成功的时间点（ unix 时间，单位毫秒）
@property (nonatomic, assign) int64_t bindTime;

/// 扩展字段
@property (nonatomic, strong) NSDictionary *extraInfo;

@end


@interface LZAccountInfo : NSObject

/// 账号：手机、邮箱、openId
@property (nonatomic, copy) NSString *account;

/// 密码 /  认证码 / token / accessCode
@property (nonatomic, copy) NSString *password;

/// 电信一键登录或者极验一键登录
@property (nonatomic, copy) NSString *authCode;

/// 账号类型
@property (nonatomic, assign) LZAuthPlatform network;

/// 第三方授权信息
@property (nonatomic, strong) LZAccountPlatInfo *plat;

- (NSDictionary *)decoding;

@end

NS_ASSUME_NONNULL_END
