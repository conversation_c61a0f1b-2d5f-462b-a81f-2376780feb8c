//
//  LZAccountReport.h
//  AuthKit
//
//  Created by yutao on 2021/10/21.
//

#import <Foundation/Foundation.h>
#import "LZAccountInfo.h"
#import "LZAccountResponse.h"

NS_ASSUME_NONNULL_BEGIN

@interface LZAccountReport : NSObject

/// 鉴权结果统计
/// @param accountInfo 账号信息
/// @param response 鉴权结果
+ (void)didAuthAccount:(LZAccountInfo *)accountInfo response:(LZAccountResponse *)response;


/// 获取验证码结果统计
/// @param account 手机号码
/// @param response 服务器返回结果
+ (void)didAuthCodeSend:(NSString *)account response:(LZAccountResponse *)response;

/// 获取国家码结果统计
/// @param response 服务器返回结果
+ (void)didQueryCountryCode:(LZAccountResponse *)response;

@end

NS_ASSUME_NONNULL_END
