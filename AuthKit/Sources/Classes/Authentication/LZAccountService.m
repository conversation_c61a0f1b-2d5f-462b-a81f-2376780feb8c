//
//  LZAccountService.m
//  AuthKit
//
//  Created by dingy<PERSON><PERSON> on 2020/8/24.
//

#import "LZAccountService.h"
#import "LZAccountResponse.h"
#import "LZAccountInfo.h"
#import "LZAuthKit.h"
#import "LZAccountReport.h"
#import <objc/runtime.h>
#import "AuthKit-swift.h"

#define kLZAuthSendIdentifyCode @"/api/verifyCode/send"
#define kLZAuthQueryCountryCode @"/api/phoneCountry/list"
#define kLZAuthAuthentication   @"/api/account/authentication"
 
@implementation LZAccountService

+ (void)_sendRequest:(NSMutableURLRequest *)req
     additionHeaders:(nullable NSDictionary<NSString *, NSString*>*)additionHeaders
  additionParameters:(nullable NSDictionary<NSString *, NSString*>*)additionParameters {
    [self  _sendRequest:req additionHeaders:additionParameters additionParameters:additionParameters mushroomEnable:LZAuthKit.mushroomEnable completionHandler:nil];
}

+ (void)_sendRequest:(NSMutableURLRequest *)req
      additionHeaders:(nullable NSDictionary<NSString *, NSString*>*)additionHeaders
   additionParameters:(nullable NSDictionary<NSString *, NSString*>*)additionParameters
      mushroomEnable: (BOOL)mushroomEnable
   completionHandler:(LZAuthServerCompletion)completionHandler {
    
    NSString *version = [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleShortVersionString"];

    NSMutableDictionary* headers = [NSMutableDictionary dictionaryWithDictionary:@{
        @"appId": [LZAuthKit appId].length ? [LZAuthKit appId] : @"",
        @"subAppId": [LZAuthKit subAppId].length ? [LZAuthKit subAppId] : @"",
        @"clientVersion": version,
        @"deviceId": [LZAuthKit deviceId].length ? [LZAuthKit deviceId] : @"",
        @"deviceType": @"ios",
        @"Content-Type": @"application/json",
        }];
    
    for (NSString *key in headers) {
        NSString *value = headers[key];
        [req setValue:value forHTTPHeaderField:key];
    }
    
    [additionHeaders enumerateKeysAndObjectsUsingBlock:^(NSString * _Nonnull key, NSString * _Nonnull value, BOOL * _Nonnull stop) {
        [req setValue:value forHTTPHeaderField:key];
    }];
    
    NSMutableDictionary *parameters = [NSMutableDictionary dictionaryWithDictionary:additionParameters];

    if (parameters.count > 0) {
        req.HTTPBody = [NSJSONSerialization dataWithJSONObject:parameters options:0 error:nil];
    }
    
    [LZAccountHTTP send:req mushroomEnable:mushroomEnable completion:^(NSData * _Nullable data, NSURLResponse * _Nullable urlResponse, NSError * _Nullable error) {
        NSDictionary* responseData = nil;
        if (data != nil) {
            responseData = [NSJSONSerialization JSONObjectWithData:data options:0 error:nil];
        }
        dispatch_async(dispatch_get_main_queue(), ^{
            if (completionHandler) {
                LZAccountResponse *_response = [[LZAccountResponse alloc] initWithURLResponse:urlResponse data:responseData error:error];
                completionHandler(_response);
            }
        });
    }];
}

#pragma mark LZAuthService

+ (void)sendIdentifyCode:(LZAuthIdentifyCodeType)type
                 account:(NSString *)account
           smsTemplateId:(nullable NSString *)tid
                    lang:(nullable NSString *)lang
              completion:(LZAuthServerCompletion)completion
{
    [self sendIdentifyCode:type account:account smsTemplateId:tid lang:lang additionParameters:nil completion:completion];
}

+ (void)sendIdentifyCode:(LZAuthIdentifyCodeType)type
                 account:(NSString *)account
           smsTemplateId:(nullable NSString *)tid
                    lang:(nullable NSString *)lang
      additionParameters:(nullable NSDictionary<NSString *, NSString*>*)additionParameters
              completion:(LZAuthServerCompletion)completion {
    [self sendIdentifyCode:type account:account smsTemplateId:tid lang:lang additionParameters:additionParameters mushroomEnable:LZAuthKit.mushroomEnable completion:completion];
}

+ (void)sendIdentifyCode:(LZAuthIdentifyCodeType)type
                 account:(NSString *)account
           smsTemplateId:(nullable NSString *)tid
                    lang:(nullable NSString *)lang
      additionParameters:(nullable NSDictionary<NSString *, NSString*>*)additionParameters
          mushroomEnable:(BOOL)mushroomEnable
              completion:(LZAuthServerCompletion)completion
{
    NSString *urlString = [[LZAuthKit serverURL] stringByAppendingString:kLZAuthSendIdentifyCode];
    NSURL *url = [NSURL URLWithString:urlString];
    NSMutableURLRequest *req = [NSMutableURLRequest requestWithURL:url];
    [req setHTTPMethod:@"POST"];
    
    NSString *sendType = @"";
    if (type == LZAuthIdentifyCodeTypePhone) {
        sendType = @"PHONE";
    } else if (type == LZAuthIdentifyCodeTypeEmail) {
        sendType = @"MAIL";
    }

    NSMutableDictionary *updatedPara = [NSMutableDictionary dictionaryWithDictionary:additionParameters];
    [updatedPara setObject:account.length > 0 ? account : @"" forKey:@"sendTo"];
    [updatedPara setObject:sendType forKey:@"sendType"];
    if (tid.length > 0) {
        [updatedPara setObject:tid forKey:@"smsTemplateId"];
    }
    if (lang.length > 0) {
        [updatedPara setObject:lang forKey:@"lang"];
    }
    
    [self _sendRequest:req additionHeaders:nil additionParameters:updatedPara mushroomEnable:mushroomEnable completionHandler:^(LZAccountResponse * _Nonnull response) {
        [LZAccountReport didAuthCodeSend:account response:response];
        completion(response);
    }];
}

+ (void)queryCountryCode:(LZAuthServerCompletion)completion {
    [self queryCountryCode:completion additionParameters:nil];
}

+ (void)queryCountryCode:(LZAuthServerCompletion)completion additionParameters:(nullable NSDictionary<NSString *, NSString*>*)additionParameters
{
    NSString *urlString = [[LZAuthKit serverURL] stringByAppendingString:kLZAuthQueryCountryCode];
    NSURL *url = [NSURL URLWithString:urlString];
    NSMutableURLRequest *req = [NSMutableURLRequest requestWithURL:url];
    [req setHTTPMethod:@"GET"];

    [self _sendRequest:req additionHeaders:nil additionParameters:additionParameters mushroomEnable:NO completionHandler:^(LZAccountResponse * _Nonnull response) {
        [LZAccountReport didQueryCountryCode:response];
        completion(response);
    }];
}

+ (void)authentication:(LZAccountInfo *)account completion:(LZAuthServerCompletion)completion
{
    [self authentication:account additionParameters:nil completion:completion];
}

+ (void)authentication:(LZAccountInfo *)account
    additionParameters:(nullable NSDictionary<NSString *, NSString*>*)additionParameters
            completion:(LZAuthServerCompletion)completion
{
    NSString *urlString = [[LZAuthKit serverURL] stringByAppendingString:kLZAuthAuthentication];
    
    NSURL *url = [NSURL URLWithString:urlString];
    NSMutableURLRequest *req = [NSMutableURLRequest requestWithURL:url];
    [req setHTTPMethod:@"POST"];

    NSDictionary *para = [account decoding];
    NSMutableDictionary *updatedPara = [NSMutableDictionary dictionaryWithDictionary:additionParameters];
    [updatedPara addEntriesFromDictionary:para];

    [self _sendRequest:req additionHeaders:nil additionParameters:updatedPara mushroomEnable:NO completionHandler:^(LZAccountResponse * _Nonnull response) {
        [LZAccountReport didAuthAccount:account response:response];
        completion(response);
    }];
}

@end


@implementation LZAccountService(DEPRECATED)

+ (void)sendIdentifyCode:(LZAuthIdentifyCodeType)type account:(NSString *)account completion:(LZAuthServerCompletion)completion
{
    [self sendIdentifyCode:type account:account smsTemplateId:nil lang:nil completion:completion];
}

@end
