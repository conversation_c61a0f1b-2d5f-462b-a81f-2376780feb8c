//
//  LZAccountReport.m
//  AuthKit
//
//  Created by yutao on 2021/10/21.
//

#import "LZAccountReport.h"
#import "LZUniqueIdentifierMgr.h"
#import "LZAuthInternalDefine.h"

@import BaseTool;

@implementation LZAccountReport

+ (void)_event:(NSString *)event label:(NSDictionary *)label
{
    [ITracker trackEvent:event label:label];
}

+ (void)didAuthAccount:(LZAccountInfo *)accountInfo response:(LZAccountResponse *)response
{
    @try {
        NSString *content = @"";
        if ([response.data isKindOfClass:NSData.class]) {
            content = [[NSString alloc] initWithData:response.data encoding:NSUTF8StringEncoding];
        }
        int64_t transactionId = [LZUniqueIdentifierMgr uniqueId];
        
        NSDictionary *parameters = @{
            @"transactionId" : @(transactionId),
            @"platform" : @(accountInfo.network),
            @"network" : @(accountInfo.network),
            @"content" : content,
            @"status" : @(response.code == 0 ? 0 : 1),
            @"errMsg" :  response.errMsg
        };
        [self _event:@"EVENT_SUPPORT_AUTH_ACCOUNT" label:parameters];
    } @catch (NSException *exception) {
        LogE(@"%@", exception.description);
    }
}

+ (void)didAuthCodeSend:(NSString *)account response:(LZAccountResponse *)response
{
    @try {
        int64_t transactionId = [LZUniqueIdentifierMgr uniqueId];
        NSDictionary *parameters = @{
            @"transactionId" : @(transactionId),
            @"phone" : account,
            @"status" : @(response.code == 0 ? 0 : 1),
            @"code" : @(response.code),
            @"errMsg" : response.errMsg
        };
        [self _event:@"EVENT_SUPPORT_AUTH_SEND_IDENTIFY" label:parameters];
    } @catch (NSException *exception) {
        LogE(@"%@", exception.description);
    }
}

+ (void)didQueryCountryCode:(LZAccountResponse *)response
{
    @try {
        int64_t transactionId = [LZUniqueIdentifierMgr uniqueId];
        NSDictionary *parameters = @{
            @"transactionId" : @(transactionId),
            @"status" : @(response.code == 0 ? 0 : 1),
            @"errMsg" : response.errMsg
        };
        [self _event:@"EVENT_SUPPORT_AUTH_COUNTRY_CODE" label:parameters];
    } @catch (NSException *exception) {
        LogE(@"%@", exception.description);
    }
}

@end
