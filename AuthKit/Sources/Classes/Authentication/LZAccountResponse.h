//
//  LZAccountResponse.h
//  LZAuthKit
//
//  Created by dingyutao on 2020/3/13.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface LZAccountResponse : NSObject

/// ------- 解析好的返回业务信息
/// 详细错误信息见文档 https://ones.lizhi.fm/wiki/#/team/6XRfASKf/share/LVrnHPuM/page/EEuP52CF

/// code
@property (nonatomic, assign) int32_t code;

/// 数据
@property (nonatomic, strong) id data;

/// 报错内容
@property (nonatomic, copy) NSString *msg;

@property (nonatomic, copy) NSString* errMsg;

/// ------- 请求回调

@property (nonatomic, strong) NSError *error;

@property (nonatomic, strong) NSURLResponse *urlResponse;

- (instancetype)initWithURLResponse:(NSURLResponse *)urlResponse data:(id _Nullable)data error:(NSError *_Nullable)error;

@end

NS_ASSUME_NONNULL_END
