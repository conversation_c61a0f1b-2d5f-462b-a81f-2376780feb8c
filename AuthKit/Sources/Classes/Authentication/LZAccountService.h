//
//  LZAccountService.h
//  AuthKit
//
//  Created by dingy<PERSON><PERSON> on 2020/8/24.
//

#import <Foundation/Foundation.h>
#import "LZAuthKitDefine.h"

#ifndef LZAccountService_h
#define LZAccountService_h

@class LZAccountResponse;
@class LZAccountInfo;

NS_ASSUME_NONNULL_BEGIN

@interface LZAccountService : NSObject

/// 国家码获取接口
/// @param completion HTTP 请求回调
+ (void)queryCountryCode:(LZAuthServerCompletion)completion;

/// 发送验证码接口
/// @param type 类型
/// @param account 账号( 86-*********** )
/// @param tid 信息模板ID EX: @"5164321726019928703"
/// @param lang 语言: en/cn 或者其他自定义值
/// @param completion HTTP 请求回调
+ (void)sendIdentifyCode:(LZAuthIdentifyCodeType)type
                 account:(NSString *)account
           smsTemplateId:(nullable NSString *)tid
                    lang:(nullable NSString *)lang
              completion:(LZAuthServerCompletion)completion;


/// 发送验证码接口
/// @param type 类型
/// @param account 账号( 86-*********** )
/// @param tid 信息模板ID EX: @"5164321726019928703"
/// @param lang 语言: en/cn 或者其他自定义值
/// @param parameters 附加请求参数（极验验证后的参数放这里）
/// 服务端逻辑提醒：会204的号码，第一次即使带了极验参数，也会返回204--服务端逻辑必须要有返回204的缓存，然后再极验验证，才能正常过风控
/// @param completion HTTP 请求回调
+ (void)sendIdentifyCode:(LZAuthIdentifyCodeType)type
                 account:(NSString *)account
           smsTemplateId:(nullable NSString *)tid
                    lang:(nullable NSString *)lang
      additionParameters:(nullable NSDictionary<NSString *, NSString*>*)parameters
              completion:(LZAuthServerCompletion)completion;



/// 鉴权接口（登录接口）
/// @param account 登录数据
/// @param completion HTTP 请求回调
+ (void)authentication:(LZAccountInfo *)account completion:(LZAuthServerCompletion)completion;

@end

NS_ASSUME_NONNULL_END

#endif


@interface LZAccountService(DEPRECATED)

/// 发送验证码接口
/// @param type 类型
/// @param account 账号( 86-*********** )
/// @param completion HTTP 请求回调
+ (void)sendIdentifyCode:(LZAuthIdentifyCodeType)type account:(NSString * _Nonnull )account completion:(LZAuthServerCompletion _Nullable )completion DEPRECATED_MSG_ATTRIBUTE("using: sendIdentifyCode:account:smsTemplateId:completion:");

@end
