//
//  LZAccountInfo.m
//  LZAuthKit
//
//  Created by dingy<PERSON><PERSON> on 2020/3/13.
//

#import "LZAccountInfo.h"

@implementation LZAccountPlatInfo

@end

@implementation LZAccountInfo

- (NSDictionary *)decoding
{
    NSMutableDictionary *para = @{}.mutableCopy;

    [para setObject:@(_network) forKey:@"network"];

    if (_authCode.length) {
        [para setObject:_authCode forKey:@"authCode"];
    }
    if (_account.length) {
        [para setObject:_account forKey:@"account"];
    }
    if (_password.length) {
        [para setObject:_password.length ? _password : @"" forKey:@"password"];
    }

    if (_plat) {
        NSMutableDictionary *bindPlatform = @{}.mutableCopy;

        LZAccountPlatInfo *plat = _plat;

        if (plat.openId.length) {
            [bindPlatform setObject:plat.openId forKey:@"openId"];
        }
        if (plat.token.length) {
            [bindPlatform setObject:plat.token forKey:@"token"];
        }
        if (plat.nickname.length) {
            [bindPlatform setObject:plat.nickname forKey:@"nickname"];
        }
        if (plat.portrait.length) {
            [bindPlatform setObject:plat.portrait forKey:@"portrait"];
        }
        if (plat.unionId.length) {
            [bindPlatform setObject:plat.unionId forKey:@"unionId"];
        }

        [para setObject:@(plat.gender) forKey:@"gender"];
        [para setObject:@(plat.expiresTime) forKey:@"expiresTime"];
        [para setObject:@(plat.bindTime) forKey:@"bindTime"];

        [para setObject:bindPlatform forKey:@"bindPlatform"];
        
        // twitter oauth2.0 需要传redirectUrl
        if (_network == LZAuthPlatformTwitterOAuth2 || _network == LZAuthPlatformTikTok) {
            NSString *redirectUrl = plat.extraInfo[@"redirectUrl"];
            if (redirectUrl && [redirectUrl isKindOfClass:NSString.class]) {
                [para setObject:redirectUrl forKey:@"callbackUrl"];
            }
        }
    }

    return [para copy];
}

@end
