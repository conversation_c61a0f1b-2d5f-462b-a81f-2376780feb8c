//
//  LZAccountResponse.m
//  LZAuthKit
//
//  Created by dingyutao on 2020/3/13.
//

#import "LZAccountResponse.h"

@implementation LZAccountResponse

- (instancetype)initWithURLResponse:(NSURLResponse *)urlResponse data:(id)data error:(NSError *)error
{
    if (self = [super init]) {
        _error = error;
        _urlResponse = urlResponse;
        _code = -1;
        [self _decodeData:data];
    }
    return self;
}

- (void)_decodeData:(id)data
{
    if (data && [data isKindOfClass:NSDictionary.class]) {
        id msg = data[@"msg"];
        if (msg && [msg isKindOfClass:NSString.class]) {
            _msg = (NSString *)msg;
        }

        _code = [data[@"code"] intValue];
        _data = data[@"data"];
    }
}

- (NSString*)errMsg {
    return self.msg.length > 0 ? self.msg : (self.error ? self.error.description : @"");
}

- (NSString *)description
{
    if (_error != NULL) {
        return [NSString stringWithFormat:@"code-%i,msg-%@,data-%@ error-%@", self.code, self.msg, self.data, self.error];
    }
    return [NSString stringWithFormat:@"code-%i,msg-%@,data-%@", self.code, self.msg, self.data];
}

@end
