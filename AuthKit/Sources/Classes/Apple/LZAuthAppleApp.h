//
//  LZAuthAppleApp.h
//  AuthKit
//
//  Created by dingyutao on 2020/8/21.
//

#import <Foundation/Foundation.h>
#import "LZAuthKitDefine.h"

#ifndef LZAuthAppleApp_h
#define LZAuthAppleApp_h

NS_ASSUME_NONNULL_BEGIN

API_AVAILABLE(ios(13.0))
@interface LZAuthAppleApp : NSObject

+ (void)authWithHandler:(LZAuthCompletionHandler)completionHandler;
+ (void)authWithHandler:(LZAuthCompletionHandler)completionHandler userInfo:(NSDictionary *)userInfo;

@end

NS_ASSUME_NONNULL_END

#endif
