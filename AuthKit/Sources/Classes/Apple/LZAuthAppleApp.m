//
//  LZAuthAppleApp.m
//  AuthKit
//
//  Created by dingyutao on 2020/8/21.
//

#import "LZAuthAppleApp.h"
#import <AuthenticationServices/AuthenticationServices.h>
#import "LZAuthUserInfo.h"
#import "LZAuthKitDefine.h"

@interface LZAuthAppleApp ()<ASAuthorizationControllerDelegate, ASAuthorizationControllerPresentationContextProviding>

@property (nonatomic,weak) UIViewController *presentingVC;
@property (nonatomic,copy) LZAuthCompletionHandler completionHandler;

@end

@implementation LZAuthAppleApp

+ (instancetype)_sharedInstance {
    static id instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[self alloc] init];
    });
    return instance;
}

+ (void)authWithHandler:(nonnull LZAuthCompletionHandler)completionHandler {
    [LZAuthAppleApp _sharedInstance].completionHandler = completionHandler;
}

+ (void)authWithHandler:(LZAuthCompletionHandler)completionHandler userInfo:(NSDictionary *)userInfo {
    [LZAuthAppleApp _sharedInstance].completionHandler = completionHandler;
    NSObject *presentingVC = userInfo[LZAuthTopViewController];
    if (presentingVC != nil && [presentingVC isKindOfClass:[UIViewController class]]) {
        [LZAuthAppleApp _sharedInstance].presentingVC = (UIViewController*)presentingVC;
    }
    [[LZAuthAppleApp _sharedInstance] authWithApple];
}

- (void)authWithApple {
    ASAuthorizationAppleIDProvider *provider = [[ASAuthorizationAppleIDProvider alloc] init];
    ASAuthorizationAppleIDRequest *request = [provider createRequest];
    [request setRequestedScopes:@[ASAuthorizationScopeEmail, ASAuthorizationScopeFullName]];
    ASAuthorizationController *vc = [[ASAuthorizationController alloc] initWithAuthorizationRequests:@[request]];
    vc.delegate = self;
    vc.presentationContextProvider = self;
    [vc performRequests];
}

#pragma mark - ASAuthorizationControllerDelegate

- (void)authorizationController:(ASAuthorizationController *)controller didCompleteWithError:(NSError *)error  API_AVAILABLE(ios(13.0)) {
    if (_completionHandler != nil) {
        LZAuthStatusCode code = LZAuthStatusCodeFail;
        switch (error.code) {
            case ASAuthorizationErrorCanceled:
                code = LZAuthStatusCodeCancel;
                break;
            case ASAuthorizationErrorFailed:
                code = LZAuthStatusCodeFail;
                break;
            default:
                break;
        }
        NSError *err = [NSError errorWithDomain:LZAuthErrorDomain
            code:code
        userInfo:@{
                   NSLocalizedDescriptionKey : [NSString stringWithFormat:@"Apple Auth: %@", error.description]
                   }];
        _completionHandler(nil, err, nil);
        _completionHandler = nil;
    }
}

- (void)authorizationController:(ASAuthorizationController *)controller didCompleteWithAuthorization:(ASAuthorization *)authorization  API_AVAILABLE(ios(13.0)) {
    if (_completionHandler != nil) {
        if ([authorization.credential isKindOfClass:[ASAuthorizationAppleIDCredential class]]) {
            ASAuthorizationAppleIDCredential *credential = authorization.credential;
            NSString *userId = credential.user;
            NSPersonNameComponents *fullName = credential.fullName;
            NSData *identityToken = credential.identityToken;
            
            NSString *identityTokenStr = [[NSString alloc] initWithData:identityToken encoding:NSUTF8StringEncoding];
            LZAuthUserInfo *authInfo = [[LZAuthUserInfo alloc] init];
            authInfo.platformId = LZAuthPlatformAppleLogin;
            authInfo.openId = userId;
            authInfo.token = identityTokenStr;
            authInfo.name = @"";
            if (fullName.givenName && fullName.givenName.length > 0) {
                authInfo.name = [NSString stringWithFormat:@"%@", fullName.givenName];
            }
            if (fullName.familyName && fullName.familyName.length > 0) {
                if (authInfo.name.length > 0) {
                    authInfo.name = [authInfo.name stringByAppendingString:@" "];
                }
                authInfo.name = [authInfo.name stringByAppendingString:fullName.familyName];
            }
            authInfo.extraInfo = @{@"AppleIDCredential": credential};
            _completionHandler(authInfo, nil, nil);
        } else {
            NSError *err = [NSError errorWithDomain:LZAuthErrorDomain
                code:LZAuthStatusCodeFail
            userInfo:@{
                       NSLocalizedDescriptionKey : [NSString stringWithFormat:@"Apple Auth: not support type"]
                       }];
            _completionHandler(nil, err, nil);
        }
        _completionHandler = nil;
    }
}

#pragma mark - ASAuthorizationControllerPresentationContextProviding

- (ASPresentationAnchor)presentationAnchorForAuthorizationController:(ASAuthorizationController *)controller  API_AVAILABLE(ios(13.0)){
    return self.presentingVC.view.window;
}

@end
