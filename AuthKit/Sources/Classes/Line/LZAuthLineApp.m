//
//  LZAuthLineApp.m
//  AuthKit
//
//  Created by sam on 2022/5/23.
//

#import "LZAuthLineApp.h"
#import "LZAuthKitDefine.h"
#import "LZAuthUserInfo.h"
@import LineSDK;

@interface LZAuthLineApp ()

@property (nonatomic,copy) LZAuthCompletionHandler completionHandler;

@end

@implementation LZAuthLineApp

+ (instancetype)_sharedInstance {
	static id instance = nil;
	static dispatch_once_t onceToken;
	dispatch_once(&onceToken, ^{
		instance = [[self alloc] init];
	});
	return instance;
}

+(void)authWithHandler:(LZAuthCompletionHandler)completionHandler userInfo:(NSDictionary *)userInfo scope:(NSSet<NSString *> *__nullable)scope {
	[LZAuthLineApp authWithHandler:completionHandler scope:scope];
}

+(void)authWithHandler:(LZAuthCompletionHandler)completionHandler scope:(NSSet<NSString *> *__nullable)scope {
    NSMutableSet *permissions = [NSMutableSet setWithObjects:[LineSDKLoginPermission openID], [LineSDKLoginPermission profile], nil];
    for (NSString *value in scope) {
        NSSet *_permissions = [LineSDKLoginPermission permissionsFrom:value];
        [permissions unionSet:_permissions];
    }
    
    [[LineSDKLoginManager sharedManager] loginWithPermissions:permissions inViewController:nil completionHandler:^(LineSDKLoginResult * _Nullable result, NSError * _Nullable error) {
        if (result == nil) {
            LZAuthStatusCode code = LZAuthStatusCodeFail;
            /// 3003 取消授权
            if (error.code == 3003) {
                code = LZAuthStatusCodeCancel;
            }
            NSError *authError = [NSError errorWithDomain:LZAuthErrorDomain
                                                     code:code
                                                 userInfo:@{NSLocalizedDescriptionKey: error }];
            completionHandler(nil, authError, nil);
        } else {
            LZAuthUserInfo *userInfo = [[LZAuthUserInfo alloc] init];
            userInfo.platformId = LZAuthPlatformLine;
            userInfo.token = result.accessToken.value;
            userInfo.expiresTime = [result.accessToken.expiresAt timeIntervalSinceNow];
            if (result.userProfile != nil) {
                userInfo.name = result.userProfile.displayName;
                userInfo.cover = [result.userProfile.pictureURL absoluteString];
                userInfo.openId = result.userProfile.userID;
                userInfo.gender = -1;
                userInfo.extraInfo = @{ @"userProfile": result.userProfile.json };
            }
            completionHandler(userInfo, nil, nil);
        }
    }];
}

+ (nonnull instancetype)applicationHandler {
	return [self _sharedInstance];
}

@end
