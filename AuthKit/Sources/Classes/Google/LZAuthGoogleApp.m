//
//  LZAuthGoogleApp.m
//  AuthKit
//
//  Created by dingyutao on 2020/8/21.
//

#import "LZAuthGoogleApp.h"
#import "LZAuthGoogleUser.h"
#import "LZAuthUserInfo.h"
#import "LZAuthInternalDefine.h"

@import GoogleSignIn;
@import social_base;

@interface LZAuthGoogleApp ()
{
    __weak UIViewController *_topViewController;
}

@property (nonatomic, copy) LZAuthCompletionHandler completionHandler;
@property (nonatomic, assign) int portroitWH;

@end

@implementation LZAuthGoogleApp

+ (id)applicationHandler
{
    return [LZAuthGoogleApp _sharedInstance];
}

+ (void)authWithHandler:(LZAuthCompletionHandler)completionHandler userInfo:(NSDictionary *)userInfo scope:(NSSet<NSString *> *__nullable)scope
{
    if (userInfo) {
        UIViewController *topViewController = userInfo[LZAuthTopViewController];
        if (topViewController) {
            [LZAuthGoogleApp _sharedInstance]->_topViewController = topViewController;
        }

        [LZAuthGoogleApp _sharedInstance].portroitWH = [userInfo[LZAuthPortraitWH] intValue];
    }

    [self authWithHandler:completionHandler scope:scope];
}


+ (void)authWithHandler:(LZAuthCompletionHandler)completionHandler scope:(NSSet<NSString *> *__nullable)scope
{
    [LZAuthGoogleApp _sharedInstance].completionHandler = completionHandler;
    NSString *appKey = @"";
    id<LZConnectorable> connector = [LZSocial connector:LZSocialPlatformGoogle];
    if(connector && [connector.appKey isKindOfClass:[NSString class]] && ![connector.appKey isEqualToString:@""]){
        appKey = connector.appKey;
    }
    
    if([connector.appKey isEqualToString:@""]) {
        [[LZAuthGoogleApp _sharedInstance] getInitializeKeyKeyError];
        return;
    }

    [[GIDSignIn sharedInstance] signOut];
    if([LZAuthGoogleApp _sharedInstance]->_topViewController){
//        GIDSignIn.sharedInstance.configuration = config;
        NSArray * scopesArr = nil;
        if (scope) {
            scopesArr = [scope allObjects];
        }
        [[GIDSignIn sharedInstance] signInWithPresentingViewController:[LZAuthGoogleApp _sharedInstance]->_topViewController hint:nil additionalScopes:scopesArr completion:^(GIDSignInResult * _Nullable signInResult, NSError * _Nullable error) {
            GIDGoogleUser *user = signInResult.user;
            if (user) {
                if ([LZAuthGoogleApp _sharedInstance].completionHandler != nil) {
                    LogI(@"signIn success");
                    
                    LZAuthUserInfo *userInfo = [[LZAuthUserInfo alloc] init];
                    userInfo.platformId = LZAuthPlatformGoogle;
                    userInfo.openId = user.userID;
                    userInfo.token = user.idToken.tokenString;
                    userInfo.name = user.profile.name;
                    userInfo.cover = [[user.profile imageURLWithDimension:[LZAuthGoogleApp _sharedInstance].portroitWH] absoluteString];
                    userInfo.gender = -1;
                    [LZAuthGoogleApp _sharedInstance].completionHandler(userInfo, nil, nil);
                    [LZAuthGoogleApp _sharedInstance].completionHandler = nil;
                }else {
                    LogE(@"signIn success but completionHandler is nil, auth suspend.");
                }
            } else {
                [[LZAuthGoogleApp _sharedInstance] _authFailWithError:error];
            }
        }];
        // GIDConfiguration *config = [[GIDConfiguration alloc] initWithClientID:((NSString *)appKey)];
        // appKey 已经在 social_base 的 initializePlatform 函数提供。这里升级后改用上面的方式调起
//        [[GIDSignIn sharedInstance] signInWithConfiguration:config presentingViewController:[LZAuthGoogleApp _sharedInstance]->_topViewController callback:^(GIDGoogleUser * _Nullable user, NSError * _Nullable error) {
//            if (user) {
//                if ([LZAuthGoogleApp _sharedInstance].completionHandler != nil) {
//                    LZAuthUserInfo *userInfo = [[LZAuthUserInfo alloc] init];
//                    userInfo.platformId = LZAuthPlatformGoogle;
//                    userInfo.openId = user.userID;
//                    userInfo.token = user.authentication.idToken;
//                    userInfo.name = user.profile.name;
//                    userInfo.cover = [[user.profile imageURLWithDimension:[LZAuthGoogleApp _sharedInstance].portroitWH] absoluteString];
//                    userInfo.gender = -1;
//                    [LZAuthGoogleApp _sharedInstance].completionHandler(userInfo, nil, nil);
//                    [LZAuthGoogleApp _sharedInstance].completionHandler = nil;
//                }
//            } else {
//                [[LZAuthGoogleApp _sharedInstance] _authFailWithError:error];
//            }
//        }];
    }else{
        NSError *error = [NSError errorWithDomain:LZAuthErrorDomain
                                             code:LZAuthStatusCodeReqTokenError
                                         userInfo:@{
            NSLocalizedDescriptionKey: @"google login need pass viewcontroller"}];
        [[LZAuthGoogleApp _sharedInstance] _authFailWithError:error];
    }

}

// MARK: - Private

+ (instancetype)_sharedInstance {
    static id instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[self alloc] init];
    });
    return instance;
}

-(void)getInitializeKeyKeyError{
    NSError *error = [NSError errorWithDomain:LZAuthErrorDomain
                                         code:LZAuthStatusCodeReqTokenError
                                     userInfo:@{
        NSLocalizedDescriptionKey: @"please make sure the class LZGoogleConnector exit and app key is have value"}];
    [[LZAuthGoogleApp _sharedInstance] _authFailWithError:error];
}

- (void)_authFailWithError:(NSError *)error
{
    if (_completionHandler != nil) {
        NSDictionary *userInfo = @{};
        
        if (error) {
            userInfo = @{ NSLocalizedDescriptionKey : error.localizedDescription,
                          LZAuthPlatformErrorKey  : error
            };
        }
        
        NSError *err = [NSError errorWithDomain:LZAuthErrorDomain
                                    code:error.code==-5?LZAuthStatusCodeCancel:LZAuthStatusCodeFail
                                userInfo:userInfo];
        
        LogI(@"signIn callback error:%@", err.description);
        _completionHandler(nil, err, nil);
        _completionHandler = nil;
    }else {
        LogE(@"_completionHandler is nil, auth suspend");
    }
}

@end
