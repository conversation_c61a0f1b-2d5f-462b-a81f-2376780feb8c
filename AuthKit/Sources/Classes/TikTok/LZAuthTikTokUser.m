//
//  LZAuthTikTokUser.m
//  AuthKit
//
//  Created by <PERSON><PERSON><PERSON> on 2021/7/9.
//

#import "LZAuthTikTokUser.h"
#import "LZAuthUserInfo.h"

@import social_base;

@implementation LZAuthTikTokUser

+ (void)parseUserInfoData:(nonnull NSData *)data toAuthInfo:(nonnull LZAuthUserInfo *)authInfo error:(NSError *__autoreleasing  _Nullable * _Nullable)error {
    if (!data) {
        if (error == nil) {
            return;
        }
        *error = [NSError errorWithDomain:LZAuthErrorDomain
                                     code:LZAuthStatusCodeReqUserError
                                 userInfo:nil];
        return;
    }

    NSDictionary *userDic = [NSJSONSerialization JSONObjectWithData:data
                                                            options:NSJSONReadingAllowFragments
                                                              error:nil];

    if ([userDic isKindOfClass:NSDictionary.class] &&
        [userDic[@"data"] isKindOfClass:NSDictionary.class] &&
        userDic[@"data"][@"display_name"]) {
        NSDictionary *data = userDic[@"data"];
        authInfo.name = data[@"display_name"];
        authInfo.cover = data[@"avatar_larger"];
        authInfo.unionId = data[@"union_id"];
        authInfo.gender = -1;
    } else {
        if (error == nil) {
            return;
        }
        NSString *errStr = [NSString stringWithFormat:@"Error: code: %ld, %@", (long)[userDic[@"errcode"] integerValue], userDic[@"errmsg"]];
        NSDictionary *userInfo = @{
            NSLocalizedDescriptionKey: errStr
        };
        *error = [NSError errorWithDomain:LZAuthErrorDomain
                                     code:LZAuthStatusCodeReqUserError
                                 userInfo:userInfo];
    }
    return;
}

+ (NSURL * _Nullable)userInfoApiWithAuthInfo:(nonnull LZAuthUserInfo *)authInfo {
    NSString *urlStr = nil;
    urlStr = [NSString stringWithFormat:@"%@?%@=%@&%@=%@",
              [[LZTikTokConnector shared] userUrl],
              @"access_token", authInfo.token,
              @"open_id", authInfo.openId
              ];
    return [NSURL URLWithString:urlStr];
}

@end
