//
//  LZAuthTikTokApp.m
//  AuthKit
//
//  Created by <PERSON>hrill<PERSON> on 2021/7/9.
//

#import "LZAuthTikTokApp.h"
#import "LZAuthUserInfo.h"
@import social_base;
#import "AuthKit-Swift.h"

@import TikTokOpenAuthSDK;

@interface LZAuthTikTokApp () {
    UIViewController *_viewController;
}

@end

@implementation LZAuthTikTokApp

+ (nonnull instancetype)applicationHandler {
    static id instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[self alloc] init];
    });
    return instance;
}

+ (void)authWithHandler:(nonnull LZAuthCompletionHandler)completionHandler scope:(NSSet<NSString *> *__nullable)scope {
    NSParameterAssert(completionHandler);
    [self requestAuthWithHandler:completionHandler scope:scope];
}

+ (void)authWithHandler:(nonnull LZAuthCompletionHandler)completionHandler userInfo:(nonnull NSDictionary *)userInfo scope:(NSSet<NSString *> *__nullable)scope {
    if (userInfo) {
        UIViewController *topViewController = userInfo[LZAuthTopViewController];
        if (topViewController) {
            LZAuthTikTokApp.applicationHandler->_viewController = topViewController;
        }
    }
    [self authWithHandler:completionHandler scope:scope];
}

+ (void)requestAuthWithHandler:(nonnull LZAuthCompletionHandler)completionHandler scope:(NSSet<NSString *> *__nullable)scope {

    static TTKSDKAuthRequest *request = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        //TODO: redirecturi 需要确定（env文件中的不是正确回调地址）
        NSString *redirectURI = LZTikTokConnector.shared.redirectUrl;
        NSSet<NSString *> *scopeSet = nil;
        if (!scope) {
            scopeSet = [NSSet setWithArray:@[KLZAuthTikTokUserInfoBasic, KLZAuthTikTokVideoList]];
        } else {
            scopeSet = scope;
        }
        request = [[TTKSDKAuthRequest alloc] initWithScopes:scopeSet redirectURI:redirectURI];
        
    });
    
    [request send:^(id<TTKSDKBaseResponse> _Nonnull response) {
        TTKSDKAuthResponse *resp = (TTKSDKAuthResponse *)response;
        if (resp.errorCode == 0) {
            
            LZAuthUserInfo *userInfo = [[LZAuthUserInfo alloc] init];
            userInfo.platformId = LZAuthPlatformTikTok;
            userInfo.authCode = resp.authCode;
            userInfo.codeVerifier = [request.pkce oc_codeVerifier];
            
            NSMutableDictionary *extraInfo = [[NSMutableDictionary alloc] init];
            [extraInfo setObject:request.redirectURI forKey:@"redirectUrl"];
            userInfo.extraInfo = extraInfo;
            
            if (completionHandler != nil) {
                completionHandler(userInfo, nil, nil);
            }
        } else {
            if (completionHandler != nil) {
                NSError *error = [NSError errorWithDomain:LZAuthErrorDomain code:LZAuthStatusCodeReqTokenError userInfo:@{
                    NSLocalizedDescriptionKey: resp.error
                }];
                completionHandler(nil, error, nil);
            }
        }
    }];
}

@end
