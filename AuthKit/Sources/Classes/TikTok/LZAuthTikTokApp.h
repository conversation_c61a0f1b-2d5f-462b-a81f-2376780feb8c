//
//  LZAuthTikTokApp.h
//  AuthKit
//
//  Created by Thrill<PERSON> on 2021/7/9.
//

#import <Foundation/Foundation.h>
#import "LZAuthProtocol.h"
#import "LZAuthTikTokUser.h"

#ifndef LZAuthTikTokApp_h
#define LZAuthTikTokApp_h

#define KLZAuthTikTokUserInfoBasic @"user.info.basic"
#define KLZAuthTikTokVideoList @"video.list"

NS_ASSUME_NONNULL_BEGIN

@interface LZAuthTikTokApp : NSObject<LZAuthPlatormable>

@end

NS_ASSUME_NONNULL_END

#endif
