//
//  LZAuthQQApp.h
//  AuthKit
//
//  Created by dingy<PERSON><PERSON> on 2020/8/21.
//

#import <Foundation/Foundation.h>
#import "LZAuthProtocol.h"

#ifndef LZAuthQQApp_h
#define LZAuthQQApp_h

#define KLZAuthQQGetSimpleUserInfo @"get_simple_userinfo"
#define KLZAuthQQAddT @"add_t"
#define KLZAuthQQDelT @"del_t"
#define KLZAuthQQAddPicT @"add_pic_t"

NS_ASSUME_NONNULL_BEGIN

@interface LZAuthQQApp : NSObject <LZAuthPlatormable>

@end

NS_ASSUME_NONNULL_END

#endif
