//
//  LZAuthQQUser.m
//  AuthKit
//
//  Created by dingyutao on 2020/8/21.
//

#import "LZAuthQQUser.h"
#import "LZAuthUserInfo.h"

@import social_base;

@implementation LZAuthQQUser

+ (void)parseUserInfoData:(NSData *)data
               toAuthInfo:(LZAuthUserInfo *)authInfo
                    error:(NSError *__autoreleasing *)error {
    if (!data){
        if (error == nil)  { return; }
        *error = [NSError errorWithDomain:LZAuthErrorDomain
                                     code:LZAuthStatusCodeReqTokenError
                                 userInfo:nil];
        return;
    }
    
    NSDictionary *userDic = [NSJSONSerialization JSONObjectWithData:data
                                                            options:NSJSONReadingAllowFragments
                                                              error:nil];
    
    if ([userDic isKindOfClass:NSDictionary.class] &&
        userDic[@"nickname"]) {
        authInfo.name = userDic[@"nickname"];
        authInfo.cover = userDic[@"figureurl_qq_2"] ?: userDic[@"figureurl_qq_1"];
        authInfo.gender = [userDic[@"gender"] isEqualToString:@"男"] ? 0 : 1;
    } else {
        if (error == nil) { return; }
        NSString *errStr = [NSString stringWithFormat:@"Error: code: %ld, %@", (long)[userDic[@"ret"] integerValue], userDic[@"errmsg"]];
        NSDictionary *userInfo = @{
                                   NSLocalizedDescriptionKey: errStr
                                   };
        *error = [NSError errorWithDomain:LZAuthErrorDomain
                                     code:LZAuthStatusCodeReqUserError
                                 userInfo:userInfo];
    }
    return;

}

+ (NSURL * _Nullable)userInfoApiWithAuthInfo:(LZAuthUserInfo *)authInfo {
    NSString *urlStr = nil;
    urlStr = [NSString stringWithFormat:@"%@?%@=%@&%@=%@&%@=%@",
              [[LZQQConnector shared] userUrl],
              @"access_token", authInfo.token,
              @"oauth_consumer_key", [[LZQQConnector shared] appKey],
              @"openid", authInfo.openId
              ];
    return [NSURL URLWithString:urlStr];
}

@end
