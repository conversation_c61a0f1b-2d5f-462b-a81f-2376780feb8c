//
//  LZAuthQQApp.m
//  AuthKit
//
//  Created by dingy<PERSON>o on 2020/8/21.
//

#import "LZAuthQQApp.h"
#import "LZAuthUserInfo.h"

#import <TencentOpenAPI/TencentOAuth.h>
#import <TencentOpenAPI/QQApiInterface.h>

@import social_base;

@interface LZAuthQQApp () <TencentSessionDelegate, QQApiInterfaceDelegate>

@property (nonatomic, copy) LZAuthCompletionHandler completionHandler;

@end

@implementation LZAuthQQApp

+ (instancetype)_sharedInstance {
    static id instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[self alloc] init];
    });
    return instance;
}

+ (id)applicationHandler {
    return [self _sharedInstance];
}

+ (void)authWithHandler:(LZAuthCompletionHandler)completionHandler scope:(NSSet<NSString *> *__nullable)scope {
    NSParameterAssert(completionHandler);

    if (!completionHandler) {
        return;
    }
    [LZAuthQQApp _sharedInstance].completionHandler = completionHandler;
    NSArray *permissions = nil;
    if (scope) {
        permissions = [scope allObjects];
    } else {
        permissions = @[KLZAuthQQGetSimpleUserInfo, KLZAuthQQAddT, KLZAuthQQDelT, KLZAuthQQAddPicT];
    }
    [[self.class oAuth] authorize:permissions];
}

+ (void)authWithHandler:(nonnull LZAuthCompletionHandler)completionHandler userInfo:(nonnull NSDictionary *)userInfo scope:(NSSet<NSString *> *__nullable)scope {
#pragma unused (userInfo)
    [self authWithHandler:completionHandler scope:scope];
}

+ (TencentOAuth *)oAuth
{
    id oAuth = [LZSocial connector:LZSocialPlatformQQ].oAuth;
    return (TencentOAuth *)oAuth;
}

// MARK: - TencentSessionDelegate

- (void)tencentDidLogin {
    if (_completionHandler != nil) {
        LZAuthUserInfo *authInfo = [[LZAuthUserInfo alloc] init];
        authInfo.platformId = LZAuthPlatformQQ;
        authInfo.token = [LZAuthQQApp oAuth].accessToken;
        authInfo.openId = [LZAuthQQApp oAuth].openId;
        authInfo.expiresTime = [[LZAuthQQApp oAuth].expirationDate timeIntervalSinceNow];
        authInfo.openKey = @"";

        _completionHandler(authInfo, nil, nil);
        _completionHandler = nil;
    }
}

- (void)tencentDidNotLogin:(BOOL)cancelled {
    if (_completionHandler != nil) {
        NSError *error = [NSError errorWithDomain:LZAuthErrorDomain
                                             code:LZAuthStatusCodeCancel
                                         userInfo:@{
                              NSLocalizedDescriptionKey: @"User cancel auth"
        }];

        _completionHandler(nil, error, nil);
        _completionHandler = nil;
    }
}

- (void)tencentDidNotNetWork {
    if (_completionHandler != nil) {
        NSError *error = [NSError errorWithDomain:LZAuthErrorDomain
                                             code:LZAuthStatusCodeNetworkError
                                         userInfo:@{
                              NSLocalizedDescriptionKey: @"RequestToken not network"
        }];
        _completionHandler(nil, error, nil);
        _completionHandler = nil;
    }
}

// MARK: - QQApiInterfaceDelegate

- (void)isOnlineResponse:(NSDictionary *)response {
}

- (void)onReq:(QQBaseReq *)req {
}

- (void)onResp:(QQBaseResp *)resp {
}

@end
