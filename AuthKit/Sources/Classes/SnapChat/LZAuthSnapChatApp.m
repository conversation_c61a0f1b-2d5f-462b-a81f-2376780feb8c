//
//  LZAuthSnapChatApp.m
//  AuthKit
//
//  Created by <PERSON>hrill<PERSON> on 2021/7/15.
//

#import "LZAuthSnapChatApp.h"
#import "LZAuthUserInfo.h"

@import SCSDKLoginKit;

@interface LZAuthSnapChatApp ()  {
    UIViewController *_viewController;
}

@end

@implementation LZAuthSnapChatApp

+ (nonnull instancetype)applicationHandler {
    static id instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[self alloc] init];
    });
    return instance;
}

+ (void)authWithHandler:(nonnull LZAuthCompletionHandler)completionHandler scope:(NSSet<NSString *> *__nullable)scope {
    NSParameterAssert(completionHandler);
    [self loginWithWithHandler:completionHandler];
}

+ (void)authWithHandler:(nonnull LZAuthCompletionHandler)completionHandler userInfo:(nonnull NSDictionary *)userInfo scope:(NSSet<NSString *> *__nullable)scope {

    if (userInfo) {
        UIViewController *topViewController = userInfo[LZAuthTopViewController];
        if (topViewController) {
            LZAuthSnapChatApp.applicationHandler->_viewController = topViewController;
        }
    }
    [self authWithHandler:completionHandler scope:scope];
}

// MARK: - Private

+ (void)loginWithWithHandler:(nonnull LZAuthCompletionHandler)completionHandler {
    [SCSDKLoginClient clearToken];
    
    //SCSDkLoginC
    [SCSDKLoginClient loginFromViewController:LZAuthSnapChatApp.applicationHandler->_viewController
                                   completion:^(BOOL success, NSError * _Nullable error) {
        if (success) {
            SCSDKUserDataQueryBuilder *builder = [[[[[SCSDKUserDataQueryBuilder alloc] init] withDisplayName] withBitmojiTwoDAvatarUrl] withExternalId];
            SCSDKUserDataQuery *userDataQuery = [builder build];
            [SCSDKLoginClient fetchUserDataWithQuery:userDataQuery success:^(SCSDKUserData * _Nullable userData, NSError * _Nullable partialError) {
                [self parseSCSDKUserData:userData error:nil isLoggedOut:NO completionHandler:completionHandler];
            } failure:^(NSError * _Nullable error, BOOL isUserLoggedOut) {
                [self parseSCSDKUserData:nil error:error isLoggedOut:isUserLoggedOut completionHandler:completionHandler];
            }];

        } else {
            dispatch_async(dispatch_get_main_queue(), ^{
                if (completionHandler) {
                    completionHandler(nil, error, nil);
                }
            });
        }
    }];
}

+ (void)parseSCSDKUserData:(SCSDKUserData *)data
                error:(NSError *)error
          isLoggedOut:(BOOL)isLoggedOut
    completionHandler:(LZAuthCompletionHandler)completionHandler {
    LZAuthUserInfo *authInfo = nil;
    if (data) {
        
        authInfo = [LZAuthUserInfo new];
        authInfo.platformId = LZAuthPlatformSnapChat;
        authInfo.name = data.displayName;
        authInfo.cover = data.bitmojiTwoDAvatarUrl;
        authInfo.openId = data.externalID;
        authInfo.token = [SCSDKLoginClient getAccessToken];
        authInfo.gender = -1;
    }
    if (completionHandler) {
        dispatch_async(dispatch_get_main_queue(), ^{
            completionHandler(authInfo,error,nil);
        });
    }
}



// MARK: - UIApplicationDelegate

- (BOOL)application:(UIApplication *)app openURL:(NSURL *)url options:(NSDictionary<UIApplicationOpenURLOptionsKey,id> *)options {
    return [SCSDKLoginClient application:app openURL:url options:options];
}

@end
