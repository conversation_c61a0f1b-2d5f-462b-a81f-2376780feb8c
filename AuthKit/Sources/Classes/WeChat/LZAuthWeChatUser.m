//
//  LZAuthWeChatUser.m
//  AuthKit
//
//  Created by dingyutao on 2020/8/21.
//

#import "LZAuthWeChatUser.h"
#import "LZAuthUserInfo.h"

@import social_base;

@implementation LZAuthWeChatUser

+ (void)parseUserInfoData:(NSData *)data
               toAuthInfo:(LZAuthUserInfo *)authInfo
                    error:(NSError *__autoreleasing *)error {
    if (!data) {
        if (error == nil) {
            return;
        }
        *error = [NSError errorWithDomain:LZAuthErrorDomain
                                     code:LZAuthStatusCodeReqUserError
                                 userInfo:nil];
        return;
    }

    NSDictionary *userDic = [NSJSONSerialization JSONObjectWithData:data
                                                            options:NSJSONReadingAllowFragments
                                                              error:nil];

    if ([userDic isKindOfClass:NSDictionary.class] &&
        userDic[@"nickname"]) {
        authInfo.name = userDic[@"nickname"];
        authInfo.cover = userDic[@"headimgurl"];
        authInfo.gender = [userDic[@"sex"] integerValue] - 1;
    } else {
        if (error == nil) {
            return;
        }
        NSString *errStr = [NSString stringWithFormat:@"Error: code: %ld, %@", (long)[userDic[@"errcode"] integerValue], userDic[@"errmsg"]];
        NSDictionary *userInfo = @{
            NSLocalizedDescriptionKey: errStr
        };
        *error = [NSError errorWithDomain:LZAuthErrorDomain
                                     code:LZAuthStatusCodeReqUserError
                                 userInfo:userInfo];
    }
    return;
}

+ (NSURL *_Nullable)userInfoApiWithAuthInfo:(LZAuthUserInfo *)authInfo {
    NSString *urlStr = nil;
    urlStr = [NSString stringWithFormat:@"%@?%@=%@&%@=%@",
              [[LZWeChatConnector shared] userUrl],
              @"access_token", authInfo.token,
              @"openid", authInfo.openId
        ];
    return [NSURL URLWithString:urlStr];
}

@end
