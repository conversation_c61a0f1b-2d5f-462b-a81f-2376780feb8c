//
//  LZAuthWeChatApp.m
//  AuthKit
//
//  Created by dingyutao on 2020/8/21.
//

#import "LZAuthWeChatApp.h"
#import "LZAuthInternalDefine.h"
#import <WXApi.h>
#import "LZAuthUserInfo.h"

@import social_base;

@interface LZAuthWeChatApp () <WXApiDelegate>

@property (nonatomic, copy) LZAuthCompletionHandler completionHandler;

@end

@implementation LZAuthWeChatApp

+ (instancetype)_sharedInstance {
    static id instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[self alloc] init];
    });
    return instance;
}

+ (id)applicationHandler {
    return [LZAuthWeChatApp _sharedInstance];
}

+ (void)authWithHandler:(LZAuthCompletionHandler)completionHandler scope:(NSSet<NSString *> *__nullable)scope {
    NSParameterAssert(completionHandler);

    if (!completionHandler) {
        return;
    }
    [LZAuthWeChatApp _sharedInstance].completionHandler = completionHandler;

    SendAuthReq *req = [[SendAuthReq alloc] init];
    NSString *scopeStr = nil;
    if (!scopeStr) {
        scopeStr = [[NSSet setWithObject:KLZAuthWeChatSNSApiUserInfo].allObjects componentsJoinedByString:@","];
    } else {
        scopeStr = [scope.allObjects componentsJoinedByString:@","];
    }
    req.scope = scopeStr;
    req.state = @"LZAuthKit";
    req.nonautomatic = NO;//允许自动授权（升级SDK，保持逻辑与旧版一致）
    [WXApi sendReq:req completion:^(BOOL success) {}];
}

+ (void)authWithHandler:(nonnull LZAuthCompletionHandler)completionHandler userInfo:(nonnull NSDictionary *)userInfo scope:(NSSet<NSString *> *__nullable)scope {
#pragma unused (userInfo)
    [self authWithHandler:completionHandler scope:scope];
}

// MARK: - WXApiDelegate

- (void)onResp:(BaseResp *)resp {
    if ([resp isKindOfClass:SendAuthResp.class]) {
        [self _receivedAuthResp:(SendAuthResp *)resp];
    }
}

// MARK: - Private Method

- (void)_receivedAuthResp:(SendAuthResp *)resp {
    if (_completionHandler != nil) {
        if (![resp isKindOfClass:SendAuthResp.class]) {
            LogE(@"WeChat should receive a 'SendAuthResp' message, but got '%@'", NSStringFromClass(resp.class));
            // FIXME: ??这里要置空 _completionHandler 吗？
            return;
        }

        SendAuthResp *authResp = (SendAuthResp *)resp;
        NSString *code = authResp.code; //获取到code

        if (code.length > 0) {//code有效
            LZAuthUserInfo *userInfo = [[LZAuthUserInfo alloc] init];
            userInfo.platformId = LZAuthPlatformWeChat;
            userInfo.authCode = code;
            _completionHandler(userInfo, nil, nil);
        } else {//code无效
            NSError *error = [NSError errorWithDomain:LZAuthErrorDomain
                                                 code:authResp.errCode
                                             userInfo:@{
                                  NSLocalizedDescriptionKey: authResp.errStr ? : @""
            }];
            _completionHandler(nil, error, nil);
            _completionHandler = nil;
        }
    }
}

@end
