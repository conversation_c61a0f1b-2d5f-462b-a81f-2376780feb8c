//
//  LZAuthWeiboApp.m
//  AuthKit
//
//  Created by dingyuta<PERSON> on 2020/8/21.
//

#import "LZAuthWeiboApp.h"
#import <WeiboSDK/WeiboSDK.h>
#import "LZAuthUserInfo.h"
@import social_base;

@interface LZAuthWeiboApp () <WeiboSDKDelegate>

@property (nonatomic, copy) LZAuthCompletionHandler completionHandler;

@end

@implementation LZAuthWeiboApp

+ (instancetype)_sharedInstance {
    static id instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[self alloc] init];
    });
    return instance;
}

+ (instancetype)applicationHandler {
    return [LZAuthWeiboApp _sharedInstance];
}

+ (void)authWithHandler:(LZAuthCompletionHandler)completionHandler scope:(NSSet<NSString *> *__nullable)scope {
    NSParameterAssert(completionHandler);
    if (!completionHandler) {
        return;
    }

    id<LZConnectorable> connector = [LZSocial connector:LZSocialPlatformWeibo];

    [LZAuthWeiboApp _sharedInstance].completionHandler = completionHandler;
    [WeiboSDK registerApp:connector.appKey universalLink:connector.ulink];
    
    NSString *scopeStr = nil;
    if (!scopeStr) {
        scopeStr = [[NSSet setWithObject:KLZAuthWeiboAll].allObjects componentsJoinedByString:@","];
    } else {
        scopeStr = [scope.allObjects componentsJoinedByString:@","];
    }
    
    WBAuthorizeRequest *request = [WBAuthorizeRequest request];
    request.redirectURI = connector.redirectUrl;
    request.scope = scopeStr;
    [WeiboSDK sendRequest:request completion:^(BOOL success) {
    }];
}

+ (void)authWithHandler:(nonnull LZAuthCompletionHandler)completionHandler userInfo:(nonnull NSDictionary *)userInfo scope:(NSSet<NSString *> *__nullable)scope {
#pragma unused (userInfo)
    [self authWithHandler:completionHandler scope:scope];
}

// MARK: - WeiboSDKDelegate
- (void)didReceiveWeiboRequest:(WBBaseRequest *)request {
}

- (void)didReceiveWeiboResponse:(WBBaseResponse *)response {
    if ([response isKindOfClass:WBAuthorizeResponse.class]) {
        [self _receivedAuthResponse:(WBAuthorizeResponse *)response];
    }
}

- (void)_receivedAuthResponse:(WBAuthorizeResponse *)resp {
    if (!_completionHandler) {
        return;
    }
    if (![resp isKindOfClass:WBAuthorizeResponse.class]) {
        return;
    }

    if (resp.statusCode == WeiboSDKResponseStatusCodeSuccess) {
        LZAuthUserInfo *info = [[LZAuthUserInfo alloc] init];
        info.platformId = LZAuthPlatformWeibo;
        info.token = resp.accessToken;
        info.openId = resp.userID;
        info.expiresTime = [resp.expirationDate timeIntervalSinceNow];
        info.openKey = @"";

        _completionHandler(info, nil, nil);
        _completionHandler = nil;
    } else {
        NSError *error = [NSError errorWithDomain:LZAuthErrorDomain
                                             code:LZAuthStatusCodeReqTokenError
                                         userInfo:@{
                              NSLocalizedDescriptionKey: [NSString stringWithFormat:@"RequestToken 见新浪微博SDK status code: %ld", (long)resp.statusCode]
        }];
        _completionHandler(nil, error, nil);
        _completionHandler = nil;
    }
}

@end
