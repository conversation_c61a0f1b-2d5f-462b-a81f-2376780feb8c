//
//  LZAuthWeiboUser.m
//  AuthKit
//
//  Created by dingyuta<PERSON> on 2020/8/21.
//

#import "LZAuthWeiboUser.h"
#import "LZAuthUserInfo.h"

@import social_base;

@implementation LZAuthWeiboUser

+ (void)parseUserInfoData:(NSData *)data
               toAuthInfo:(LZAuthUserInfo *)authInfo
                    error:(NSError *__autoreleasing *)error {
    if (!data) {
        if (error == nil) {
            return;
        }
        *error = [NSError errorWithDomain:LZAuthErrorDomain
                                     code:LZAuthStatusCodeReqTokenError
                                 userInfo:nil];
        return;
    }

    NSDictionary *userDic = [NSJSONSerialization JSONObjectWithData:data
                                                            options:NSJSONReadingAllowFragments
                                                              error:nil];

    if ([userDic isKindOfClass:NSDictionary.class] &&
        userDic[@"screen_name"]) {
        authInfo.name = userDic[@"screen_name"];
        authInfo.cover = userDic[@"avatar_hd"];
        authInfo.gender = [userDic[@"gender"] isEqualToString:@"m"] ? 0 : 1;
    } else {
        if (error == nil) {
            return;
        }
        NSString *errStr = [NSString stringWithFormat:@"Error: code: %ld, %@", (long)[userDic[@"error_code"] integerValue], userDic[@"error"]];
        NSDictionary *userInfo = @{
            NSLocalizedDescriptionKey: errStr
        };
        *error = [NSError errorWithDomain:LZAuthErrorDomain
                                     code:LZAuthStatusCodeReqUserError
                                 userInfo:userInfo];
    }
    return;
}

+ (NSURL *_Nullable)userInfoApiWithAuthInfo:(LZAuthUserInfo *)authInfo {
    id<LZConnectorable> connector = [LZSocial connector:LZSocialPlatformWeibo];

    NSString *urlStr = [NSString stringWithFormat:@"%@?%@=%@&%@=%@",
                        connector.userUrl,
                        @"access_token", authInfo.token,
                        @"uid", authInfo.openId
        ];
    return [NSURL URLWithString:urlStr];
}

@end
