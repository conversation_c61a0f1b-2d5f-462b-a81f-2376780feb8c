//
//  LZAuthTwitterOAuth2App.h
//  AuthKit
//
//  Created by yutao on 2023/5/15.
//

#import "LZAuthTwitterOAuth2App.h"
#import "LZAuthKitDefine.h"
#import "LZAuthUserInfo.h"
#import <AuthenticationServices/AuthenticationServices.h>

@import social_base;

API_AVAILABLE(ios(12.0))
@interface LZAuthTwitterOAuth2App ()<ASWebAuthenticationPresentationContextProviding>

@property (nonatomic,copy) LZAuthCompletionHandler completionHandler;

@property (nonatomic, strong) ASWebAuthenticationSession *session;

@end

@implementation LZAuthTwitterOAuth2App

+ (instancetype)_sharedInstance {
    static id instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[self alloc] init];
    });
    return instance;
}

+(void)authWithHandler:(LZAuthCompletionHandler)completionHandler userInfo:(NSDictionary *)userInfo scope:(NSSet<NSString *> *__nullable)scope {
    [LZAuthTwitterOAuth2App authWithHandler:completionHandler scope:scope];
}

+(void)authWithHandler:(LZAuthCompletionHandler)completionHandler scope:(NSSet<NSString *> *__nullable)scope {
    [LZAuthTwitterOAuth2App _sharedInstance].completionHandler = completionHandler;
    
    if ([LZAuthTwitterOAuth2App _sharedInstance].session != nil) {
        [[LZAuthTwitterOAuth2App _sharedInstance].session cancel];
        [LZAuthTwitterOAuth2App _sharedInstance].session = nil;
    }
    
    NSURL *url = LZTwitterOAuth2Connector.shared.oAuth;
    if (!url || ![url isKindOfClass:NSURL.class]) {
        NSError *error = [NSError errorWithDomain:LZAuthErrorDomain
                                             code:LZAuthStatusCodeNotInitialize
                                         userInfo:@{NSLocalizedDescriptionKey : @"NotInitialize TwitterOAuth2"}];
        [self authFail:error];
        return;
    }
    
    NSURL *redirectUrl = [NSURL URLWithString:LZTwitterOAuth2Connector.shared.redirectUrl];
    NSString *redirectScheme = [redirectUrl scheme];
    
    if (@available(iOS 13.0, *)) {
        [LZAuthTwitterOAuth2App _sharedInstance].session = [[ASWebAuthenticationSession alloc] initWithURL:url callbackURLScheme:redirectScheme completionHandler:^(NSURL * _Nullable callbackURL, NSError * _Nullable error) {
            if (callbackURL.absoluteString.length != 0) {
                [self authSuccess:callbackURL];
            }else if (error) {
                [self authFail:error];
            }
        }];
        [LZAuthTwitterOAuth2App _sharedInstance].session.presentationContextProvider = [LZAuthTwitterOAuth2App _sharedInstance];
        [[LZAuthTwitterOAuth2App _sharedInstance].session start];
    } else {
        // Fallback on earlier versions
        NSError *error = [NSError errorWithDomain:LZAuthErrorDomain
                                             code:LZAuthStatusCodeUnsupportOSVersion
                                         userInfo:@{NSLocalizedDescriptionKey : @"Unsupport os version"}];
        [self authFail:error];
    }
}

+ (nonnull instancetype)applicationHandler {
    return [self _sharedInstance];
}

///// 验证成功
///// @param callbackURL 回调URL
+ (void)authSuccess:(NSURL *)callbackURL {
    
    NSURLComponents *callbackComponents = [NSURLComponents componentsWithURL:callbackURL resolvingAgainstBaseURL:NO];
    NSArray<NSURLQueryItem *> *items = callbackComponents.queryItems;
    NSString *code = @"";
    NSString *errMsg = @"";
    for (NSURLQueryItem *item in items) {
        if ([item.name isEqualToString:@"code"]) {
            code = item.value;
        }
        if ([item.name isEqualToString:@"error"]) {
            errMsg = item.value;
        }
    }
    // 回调地址中带错误内容
    if (errMsg.length > 0) {
        NSError *error = [NSError errorWithDomain:LZAuthErrorDomain
                                             code:LZAuthStatusCodeFail
                                         userInfo:@{NSLocalizedDescriptionKey : errMsg}];
        [self authFail:error];
        return;
    }
    LZAuthUserInfo *userInfo = [[LZAuthUserInfo alloc] init];
    userInfo.platformId = LZAuthPlatformTwitterOAuth2;
    userInfo.openId = code;
    NSString *codeVerifier = LZTwitterOAuth2Connector.shared.userInfo[@"codeVerifier"];
    userInfo.token = codeVerifier;
    userInfo.extraInfo = @{@"redirectUrl": LZTwitterOAuth2Connector.shared.redirectUrl};
    
    dispatch_async(dispatch_get_main_queue(), ^{
        if ([LZAuthTwitterOAuth2App _sharedInstance].completionHandler) {
            [LZAuthTwitterOAuth2App _sharedInstance].completionHandler(userInfo,nil,nil);
        }
    });
    
    [LZAuthTwitterOAuth2App _sharedInstance].session = nil;
}

///// 验证失败
///// @param error 错误原因
+(void)authFail:(NSError *)error {
	LZAuthStatusCode code = LZAuthStatusCodeFail;
	if(error.code == 1) {
		code = LZAuthStatusCodeCancel;
	}
	NSError *err = [NSError errorWithDomain:LZAuthErrorDomain
	                code:code
	                userInfo:@{
	                        NSLocalizedDescriptionKey : [NSString stringWithFormat:@"Twitter Auth: %@", error.description]
	}];

    dispatch_async(dispatch_get_main_queue(), ^{
        if ([LZAuthTwitterOAuth2App _sharedInstance].completionHandler) {
            [LZAuthTwitterOAuth2App _sharedInstance].completionHandler(nil,err,nil);
        }
    });
    
    [LZAuthTwitterOAuth2App _sharedInstance].session = nil;
}


- (nonnull ASPresentationAnchor)presentationAnchorForWebAuthenticationSession:(nonnull ASWebAuthenticationSession *)session  API_AVAILABLE(ios(13.0)) {
    return [[UIApplication sharedApplication] keyWindow];
}

@end
