
stages:
  - analyze
  - podlink


workflow:
  rules:
    #Merge中的，自动检查
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event' && ($CI_MERGE_REQUEST_TARGET_BRANCH_NAME == 'master' || $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == 'develop')
    #定时
    - if: $CI_PIPELINE_SOURCE == "schedule"
    #手动
    - if: $CI_JOB_MANUAL

variables:
  ANALYZE_PROJECT_DIR: $CI_PROJECT_DIR/Example
#   ANALYZE_PROJECT_NAME: 'ITNetExample'
#   ANALYZE_PROJECT_SCHEME: 'ITNetExample'
#   ANALYZE_PROJECT_DIR: '$CI_PROJECT_DIR/Example'
#   ANALYZE_BUILD_DIR: '$ANALYZE_PROJECT_DIR/analyze_build'


# pre_job:
#   stage: .pre
#   script:
#     - 'cd $CI_PROJECT_DIR/Example'
#   rules:
#     - when: always
#   tags:
#     - flash


analyze_job:
  stage: analyze
  script:
    - cd $CI_PROJECT_DIR
    - wget -O gitlab_analyze.py https://gitlab.lizhi.fm/flash_script/flash_cicd/-/raw/master/script_ios/analyze.py
    - chmod +x gitlab_analyze.py
    - gitlab_analyze.py --path $ANALYZE_PROJECT_DIR
  artifacts:
    when: always
    name: analyzed-$CI_JOB_STARTED_AT
    # paths: ['./Example/*_cc_analyzed.json', './Example/*_cc_analyzed_html']
    paths:
      - $ANALYZE_PROJECT_DIR/*_cc_analyzed.json
      - $ANALYZE_PROJECT_DIR/*_cc_analyzed_html
    expire_in: 3 days
  # rules:
  #   - if: $CI_PIPELINE_SOURCE == 'merge_request_event' && ($CI_MERGE_REQUEST_TARGET_BRANCH_NAME == 'master' || $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == 'develop')
  #   - if: $CI_PIPELINE_SOURCE == "schedule"
  #   - if: $CI_JOB_MANUAL
  when: always
  tags:
    - flash

podlink_job:
  stage: podlink
  script:
    - cd $CI_PROJECT_DIR
    - wget -O gitlab_podlint.sh https://gitlab.lizhi.fm/flash_script/flash_cicd/-/raw/master/script_ios/podlint.sh
    - chmod +x gitlab_podlint.sh
    - sh gitlab_podlint.sh
  # rules:
  #   - if: $CI_PIPELINE_SOURCE == 'merge_request_event' && ($CI_MERGE_REQUEST_TARGET_BRANCH_NAME == 'master' || $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == 'develop')
  #   - if: $CI_PIPELINE_SOURCE == "schedule"
  #     when: always
  #   - if: $CI_JOB_MANUAL
  when: on_success
  tags:
    - flash

# post_job:
#   stage: .post
#   script:
#     - cd $CI_PROJECT_DIR
#     - echo post_job is empty
#   when:
#     always
#   tags:
#     - flash
